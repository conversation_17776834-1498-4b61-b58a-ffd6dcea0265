//+------------------------------------------------------------------+
//| FibReversal_v4_clean.mq5                                        |
//| Clean synthesized EA combining profitability of v3 with v1 logic|
//| Based on onurFibo.py - Fibonacci Retracement Trading Strategy   |
//+------------------------------------------------------------------+
#property copyright "Synthesized from v1 and v3"
#property version   "4.00"

#include <Trade\Trade.mqh>

// Strategy parameters - Using v3's profitable 3% threshold
input double InpPriceChangePercentage = 3.0;          // Price change threshold percentage (v3's profitable setting)
input double InpFibonacciLevel       = 0.236;         // Fibonacci retracement level for entries
input double InpHardStopPercentage   = 1.5;           // Hard stop percentage
input bool   InpUseFixedLotSize      = false;         // Use fixed lot size instead of risk percentage
input double InpFixedLotSize         = 0.01;          // Fixed lot size (when InpUseFixedLotSize = true)
input double InpRiskPercent          = 2.0;           // Risk percentage per trade (when InpUseFixedLotSize = false)

// Trading phases
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE,      // Looking for pattern signals
   PHASE_ACTIVE_LONG,  // Looking for bullish fibonacci setup
   PHASE_ACTIVE_SHORT  // Looking for bearish fibonacci setup
};

// Global variables
CTrade trade;
int handle_fractals;

// Strategy state variables
ENUM_TRADING_PHASE tradingPhase = PHASE_PASSIVE;

// Fibonacci range tracking (v3 style)
double fibHighPrice = 0;
double fibLowPrice = 0;
double fibLevel236 = 0;
double fibLevel500 = 0;

// Fractal tracking (v1 style for validation)
double firstFractalPrice = 0;
double secondFractalPrice = 0;
bool firstFractalIsUp = false;
bool secondFractalIsUp = false;
datetime firstFractalTime = 0;
datetime secondFractalTime = 0;

// Position tracking
ulong longTicket = 0;
ulong shortTicket = 0;
double longEntryPrice = 0;
double shortEntryPrice = 0;

// Bar tracking for pattern detection
datetime lastProcessedBarTime = 0;
bool waitingForNewBar = false;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   // Validate parameters
   if(InpPriceChangePercentage <= 0)
   {
      printf("Price change percentage must be greater than 0, current value: %f", InpPriceChangePercentage);
      return INIT_PARAMETERS_INCORRECT;
   }
   
   if(InpFibonacciLevel <= 0 || InpFibonacciLevel >= 1)
   {
      printf("Fibonacci level must be between 0 and 1, current value: %f", InpFibonacciLevel);
      return INIT_PARAMETERS_INCORRECT;
   }
   
   if(InpHardStopPercentage <= 0)
   {
      printf("Hard stop percentage must be greater than 0, current value: %f", InpHardStopPercentage);
      return INIT_PARAMETERS_INCORRECT;
   }

   // Create Fractals indicator on 4H timeframe
   handle_fractals = iFractals(NULL, PERIOD_H4);
   if(handle_fractals == INVALID_HANDLE)
   {
      printf("Error creating Fractals indicator on 4H timeframe");
      return INIT_FAILED;
   }

   // Setup trade object
   trade.SetExpertMagicNumber(12345);
   trade.SetMarginMode();
   trade.SetTypeFillingBySymbol(Symbol());

   printf("=== FibReversal v4 Clean EA Initialized ===");
   printf("Price Change Threshold: %.1f%% (v3's profitable setting)", InpPriceChangePercentage);
   printf("Fibonacci Entry Level: %.3f", InpFibonacciLevel);
   printf("Hard Stop Percentage: %.1f%%", InpHardStopPercentage);
   printf("Using hybrid approach: v3's pattern detection + v1's fractal validation");

   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   if(handle_fractals != INVALID_HANDLE)
      IndicatorRelease(handle_fractals);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Process based on current trading phase
   if(tradingPhase == PHASE_PASSIVE)
   {
      ProcessPassivePhase();
   }
   else
   {
      ProcessActivePhase();
   }
   
   // Log heartbeat every hour
   LogHeartbeat();
}

//+------------------------------------------------------------------+
//| Process passive phase - Hybrid pattern detection               |
//+------------------------------------------------------------------+
void ProcessPassivePhase()
{
   // Check if we're waiting for a new 4H bar
   if(waitingForNewBar)
   {
      datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
      if(currentBarTime != lastProcessedBarTime)
      {
         waitingForNewBar = false;
         lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - ready to start pattern detection");
      }
      else
      {
         return;
      }
   }

   DetectPatternsHybrid();
}

//+------------------------------------------------------------------+
//| Hybrid pattern detection - v3's 3-bar + v1's fractal validation|
//+------------------------------------------------------------------+
void DetectPatternsHybrid()
{
   // Check if we need to wait for a new 4H bar
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
   if(waitingForNewBar)
   {
      if(currentBarTime == lastProcessedBarTime)
         return;
      else
      {
         waitingForNewBar = false;
         lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - starting hybrid pattern detection");
      }
   }

   // STEP 1: v3's Python-style 3-bar close pattern detection
   double close0 = iClose(NULL, PERIOD_H4, 2); // 2 bars ago
   double close1 = iClose(NULL, PERIOD_H4, 1); // 1 bar ago  
   double close2 = iClose(NULL, PERIOD_H4, 0); // Current bar

   printf("DEBUG: 3-bar pattern check - close0=%.5f, close1=%.5f, close2=%.5f", close0, close1, close2);

   // Check for mode switching patterns (Python logic)
   bool bullishPattern = (close0 > close1 && close1 > close2); // Bearish trend → look for bullish fib
   bool bearishPattern = (close0 < close1 && close1 < close2); // Bullish trend → look for bearish fib

   if(bullishPattern)
   {
      printf("BULLISH PATTERN DETECTED: close0 > close1 > close2 - validating with fractals");
      
      // STEP 2: Validate with fractal detection (v1 style)
      if(ValidateWithFractals())
      {
         tradingPhase = PHASE_ACTIVE_LONG;
         
         // Initialize peak from last 3 bars (v3 style)
         double high0 = iHigh(NULL, PERIOD_H4, 2);
         double high1 = iHigh(NULL, PERIOD_H4, 1);
         double high2 = iHigh(NULL, PERIOD_H4, 0);
         fibHighPrice = MathMax(high0, MathMax(high1, high2));
         
         printf("BULLISH MODE ACTIVATED: Initial peak locked at %.5f", fibHighPrice);
         waitingForNewBar = true;
      }
   }
   else if(bearishPattern)
   {
      printf("BEARISH PATTERN DETECTED: close0 < close1 < close2 - validating with fractals");
      
      // STEP 2: Validate with fractal detection (v1 style)
      if(ValidateWithFractals())
      {
         tradingPhase = PHASE_ACTIVE_SHORT;
         
         // Initialize trough from last 3 bars (v3 style)
         double low0 = iLow(NULL, PERIOD_H4, 2);
         double low1 = iLow(NULL, PERIOD_H4, 1);
         double low2 = iLow(NULL, PERIOD_H4, 0);
         fibLowPrice = MathMin(low0, MathMin(low1, low2));
         
         printf("BEARISH MODE ACTIVATED: Initial trough locked at %.5f", fibLowPrice);
         waitingForNewBar = true;
      }
   }
}

//+------------------------------------------------------------------+
//| Validate pattern with fractal detection (v1 approach)          |
//+------------------------------------------------------------------+
bool ValidateWithFractals()
{
   // Get recent fractal data
   double upFractals[6];
   double downFractals[6];

   if(CopyBuffer(handle_fractals, 0, 0, 6, upFractals) != 6)
      return false;
   if(CopyBuffer(handle_fractals, 1, 0, 6, downFractals) != 6)
      return false;

   // Look for recent fractals to validate the pattern
   for(int i = 2; i < 5; i++)
   {
      bool upFractal = (upFractals[i] != EMPTY_VALUE && upFractals[i] != 0);
      bool downFractal = (downFractals[i] != EMPTY_VALUE && downFractals[i] != 0);

      if(upFractal || downFractal)
      {
         double fractalPrice = upFractal ? upFractals[i] : downFractals[i];
         bool fractalIsUp = upFractal;
         datetime fractalTime = iTime(NULL, PERIOD_H4, i);

         printf("DEBUG: Fractal validation - %s fractal at %.5f", fractalIsUp ? "UP" : "DOWN", fractalPrice);

         // Process the fractal using v1's logic
         bool fractalProcessed = ProcessNewFractal(fractalPrice, fractalIsUp, fractalTime);

         if(fractalProcessed)
         {
            // Check if we have a valid fractal couple with sufficient range
            if(firstFractalPrice != 0 && secondFractalPrice != 0)
            {
               double priceChange = MathAbs(firstFractalPrice - secondFractalPrice) / MathMin(firstFractalPrice, secondFractalPrice) * 100.0;

               if(priceChange >= InpPriceChangePercentage)
               {
                  printf("Fractal validation PASSED: %.2f%% range (threshold: %.2f%%)", priceChange, InpPriceChangePercentage);
                  return true;
               }
               else
               {
                  printf("Fractal validation FAILED: %.2f%% range below threshold %.2f%%", priceChange, InpPriceChangePercentage);
               }
            }
         }
      }
   }

   printf("Fractal validation FAILED: No valid fractal couple found");
   return false;
}

//+------------------------------------------------------------------+
//| Process new fractal (v1 logic)                                  |
//+------------------------------------------------------------------+
bool ProcessNewFractal(double price, bool isUp, datetime time)
{
   bool fractalUpdated = false;

   if(firstFractalPrice == 0) // No first fractal yet
   {
      firstFractalPrice = price;
      firstFractalIsUp = isUp;
      firstFractalTime = time;
      printf("First fractal set: %s at %.5f", isUp ? "UP" : "DOWN", price);
      fractalUpdated = true;
   }
   else if(secondFractalPrice == 0) // Have first, need second
   {
      if(isUp != firstFractalIsUp) // Opposite direction - potential second fractal
      {
         secondFractalPrice = price;
         secondFractalIsUp = isUp;
         secondFractalTime = time;
         printf("Second fractal set: %s at %.5f", isUp ? "UP" : "DOWN", price);
         fractalUpdated = true;
      }
      else // Same direction - update first fractal if more extreme
      {
         if((isUp && price > firstFractalPrice) || (!isUp && price < firstFractalPrice))
         {
            firstFractalPrice = price;
            firstFractalTime = time;
            printf("First fractal updated to more extreme: %s at %.5f", isUp ? "UP" : "DOWN", price);
            fractalUpdated = true;
         }
      }
   }
   else // Have both fractals - update the appropriate one if more extreme
   {
      double currentHigh = MathMax(firstFractalPrice, secondFractalPrice);
      double currentLow = MathMin(firstFractalPrice, secondFractalPrice);

      if(isUp && price > currentHigh) // New higher high
      {
         if(firstFractalPrice >= secondFractalPrice)
         {
            firstFractalPrice = price;
            firstFractalTime = time;
            firstFractalIsUp = isUp;
         }
         else
         {
            secondFractalPrice = price;
            secondFractalTime = time;
            secondFractalIsUp = isUp;
         }
         printf("Updated high fractal to %.5f", price);
         fractalUpdated = true;
      }
      else if(!isUp && price < currentLow) // New lower low
      {
         if(firstFractalPrice <= secondFractalPrice)
         {
            firstFractalPrice = price;
            firstFractalTime = time;
            firstFractalIsUp = isUp;
         }
         else
         {
            secondFractalPrice = price;
            secondFractalTime = time;
            secondFractalIsUp = isUp;
         }
         printf("Updated low fractal to %.5f", price);
         fractalUpdated = true;
      }
   }

   return fractalUpdated;
}

//+------------------------------------------------------------------+
//| Process active phase - Handle fibonacci setups                  |
//+------------------------------------------------------------------+
void ProcessActivePhase()
{
   // Check for existing positions first
   if(longTicket != 0)
   {
      if(!LongClosed())
         return; // Position still active
      else
      {
         // Position closed, reset and return to passive
         ResetStrategy();
         return;
      }
   }

   if(shortTicket != 0)
   {
      if(!ShortClosed())
         return; // Position still active
      else
      {
         // Position closed, reset and return to passive
         ResetStrategy();
         return;
      }
   }

   // Update peak/trough tracking (v3 style with v1 validation)
   UpdatePeakTroughTracking();

   // Try to open positions based on fibonacci levels
   if(tradingPhase == PHASE_ACTIVE_LONG && fibLevel236 > 0)
   {
      LongOpened();
   }
   else if(tradingPhase == PHASE_ACTIVE_SHORT && fibLevel236 > 0)
   {
      ShortOpened();
   }
}

//+------------------------------------------------------------------+
//| Update peak/trough tracking - Hybrid v3 + v1 approach         |
//+------------------------------------------------------------------+
void UpdatePeakTroughTracking()
{
   if(tradingPhase == PHASE_PASSIVE)
      return;

   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   if(tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python style reset logic: Only reset if fibonacci candidate exists AND price moves outside range
      double currentClose = iClose(NULL, PERIOD_H4, 0);
      if(fibLevel236 > 0 && (currentClose < fibLowPrice || currentClose > fibHighPrice))
      {
         printf("RESET: Close %.5f outside range [%.5f - %.5f] with active fib - returning to passive",
                currentClose, fibLowPrice, fibHighPrice);
         ResetStrategy();
         return;
      }

      // v3 style: Continuously update peak like Python script
      if(currentHigh > fibHighPrice)
      {
         fibHighPrice = currentHigh;
         printf("Peak updated to %.5f (bullish mode)", fibHighPrice);
      }

      // Check if we meet the minimum range requirement using current peak and current low
      double priceChange = (fibHighPrice - currentLow) / currentLow * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(fibLowPrice != currentLow || fibLevel236 == 0)
         {
            fibLowPrice = currentLow;
            printf("Bullish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   fibHighPrice, fibLowPrice, priceChange);
            CalculateFibonacciLevels();
         }
      }
   }
   else if(tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // Python style reset logic: Only reset if fibonacci candidate exists AND price moves outside range
      double currentClose = iClose(NULL, PERIOD_H4, 0);
      if(fibLevel236 > 0 && (currentClose > fibHighPrice || currentClose < fibLowPrice))
      {
         printf("RESET: Close %.5f outside range [%.5f - %.5f] with active fib - returning to passive",
                currentClose, fibLowPrice, fibHighPrice);
         ResetStrategy();
         return;
      }

      // v3 style: Continuously update trough like Python script
      if(currentLow < fibLowPrice)
      {
         fibLowPrice = currentLow;
         printf("Trough updated to %.5f (bearish mode)", fibLowPrice);
      }

      // Check if we meet the minimum range requirement using current high and current trough
      double priceChange = (currentHigh - fibLowPrice) / fibLowPrice * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(fibHighPrice != currentHigh || fibLevel236 == 0)
         {
            fibHighPrice = currentHigh;
            printf("Bearish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   fibHighPrice, fibLowPrice, priceChange);
            CalculateFibonacciLevels();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate Fibonacci levels (same logic as both v1 and v3)      |
//+------------------------------------------------------------------+
void CalculateFibonacciLevels()
{
   if(fibHighPrice == 0 || fibLowPrice == 0)
   {
      printf("ERROR: Cannot calculate Fibonacci levels - invalid range");
      return;
   }

   double fibRange = fibHighPrice - fibLowPrice;

   // Calculate Fibonacci retracement levels based on trading phase
   if(tradingPhase == PHASE_ACTIVE_LONG)
   {
      // For LONG setups (after price dropped): retracement UP from the low
      fibLevel236 = fibLowPrice + (fibRange * InpFibonacciLevel);
      fibLevel500 = fibLowPrice + (fibRange * 0.5);
      printf("LONG setup - Fibonacci levels calculated (retracement UP from low):");
   }
   else if(tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // For SHORT setups (after price rose): retracement DOWN from the high
      fibLevel236 = fibHighPrice - (fibRange * InpFibonacciLevel);
      fibLevel500 = fibHighPrice - (fibRange * 0.5);
      printf("SHORT setup - Fibonacci levels calculated (retracement DOWN from high):");
   }
   else
   {
      printf("ERROR: CalculateFibonacciLevels called in PASSIVE phase");
      return;
   }

   printf("  Range: %.5f (High: %.5f - Low: %.5f)", fibRange, fibHighPrice, fibLowPrice);
   printf("  Fib 0.236 (%.3f): %.5f", InpFibonacciLevel, fibLevel236);
   printf("  Fib 0.500: %.5f", fibLevel500);
   double currentBid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double currentAsk = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   printf("  Current Bid: %.5f, Ask: %.5f", currentBid, currentAsk);
}

//+------------------------------------------------------------------+
//| Check for long position opening (v3 style)                     |
//+------------------------------------------------------------------+
void LongOpened()
{
   if(longTicket != 0 || tradingPhase != PHASE_ACTIVE_LONG || fibLevel236 == 0)
      return;

   // Check if 4H close is above fibonacci 236 level (Python style activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h > fibLevel236)
   {
      printf("BULLISH ACTIVATION: Close %.5f > Fib 236 %.5f - opening long position", close4h, fibLevel236);

      double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      double hardStopDistance = ask * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      longEntryPrice = ask;

      printf("Opening long position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             ask, fibLevel236, fibLevel500, lotSize);

      if(trade.Buy(lotSize, Symbol(), ask))
      {
         longTicket = trade.ResultOrder();
         printf("Long position opened successfully, ticket: %I64u", longTicket);
      }
      else
      {
         printf("Error opening long position: %s", trade.ResultComment());
      }
   }
}

//+------------------------------------------------------------------+
//| Check for short position opening (v3 style)                    |
//+------------------------------------------------------------------+
void ShortOpened()
{
   if(shortTicket != 0 || tradingPhase != PHASE_ACTIVE_SHORT || fibLevel236 == 0)
      return;

   // Check if 4H close is below fibonacci 236 level (Python style activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h < fibLevel236)
   {
      printf("BEARISH ACTIVATION: Close %.5f < Fib 236 %.5f - opening short position", close4h, fibLevel236);

      double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      double hardStopDistance = bid * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      shortEntryPrice = bid;

      printf("Opening short position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             bid, fibLevel236, fibLevel500, lotSize);

      if(trade.Sell(lotSize, Symbol(), bid))
      {
         shortTicket = trade.ResultOrder();
         printf("Short position opened successfully, ticket: %I64u", shortTicket);
      }
      else
      {
         printf("Error opening short position: %s", trade.ResultComment());
      }
   }
}

//+------------------------------------------------------------------+
//| Check if long position should be closed (Python style)         |
//+------------------------------------------------------------------+
bool LongClosed()
{
   if(longTicket == 0)
      return true;

   if(!PositionSelectByTicket(longTicket))
   {
      printf("Long position ticket %I64u not found - assuming closed", longTicket);
      longTicket = 0;
      return true;
   }

   double currentBid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   // Python style exit conditions
   bool hardStopHit = (currentLow < fibLevel236 * (1 - InpHardStopPercentage / 100.0));
   bool targetReached = (currentBid >= fibLevel500);

   if(hardStopHit)
   {
      printf("LONG HARD STOP: Low %.5f < Stop %.5f - closing position",
             currentLow, fibLevel236 * (1 - InpHardStopPercentage / 100.0));

      if(trade.PositionClose(longTicket))
      {
         printf("Long position closed by hard stop, ticket: %I64u", longTicket);
         longTicket = 0;
         return true;
      }
   }
   else if(targetReached)
   {
      printf("LONG TARGET: Bid %.5f >= Target %.5f - closing position", currentBid, fibLevel500);

      if(trade.PositionClose(longTicket))
      {
         printf("Long position closed at target, ticket: %I64u", longTicket);
         longTicket = 0;
         return true;
      }
   }

   return false; // Position still active
}

//+------------------------------------------------------------------+
//| Check if short position should be closed (Python style)        |
//+------------------------------------------------------------------+
bool ShortClosed()
{
   if(shortTicket == 0)
      return true;

   if(!PositionSelectByTicket(shortTicket))
   {
      printf("Short position ticket %I64u not found - assuming closed", shortTicket);
      shortTicket = 0;
      return true;
   }

   double currentAsk = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);

   // Python style exit conditions
   bool hardStopHit = (currentHigh > fibLevel236 * (1 + InpHardStopPercentage / 100.0));
   bool targetReached = (currentAsk <= fibLevel500);

   if(hardStopHit)
   {
      printf("SHORT HARD STOP: High %.5f > Stop %.5f - closing position",
             currentHigh, fibLevel236 * (1 + InpHardStopPercentage / 100.0));

      if(trade.PositionClose(shortTicket))
      {
         printf("Short position closed by hard stop, ticket: %I64u", shortTicket);
         shortTicket = 0;
         return true;
      }
   }
   else if(targetReached)
   {
      printf("SHORT TARGET: Ask %.5f <= Target %.5f - closing position", currentAsk, fibLevel500);

      if(trade.PositionClose(shortTicket))
      {
         printf("Short position closed at target, ticket: %I64u", shortTicket);
         shortTicket = 0;
         return true;
      }
   }

   return false; // Position still active
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CalculateLotSize(double stopLossDistance)
{
   if(InpUseFixedLotSize)
      return InpFixedLotSize;

   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpRiskPercent / 100.0;
   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

   if(tickValue == 0 || tickSize == 0 || stopLossDistance == 0)
      return InpFixedLotSize;

   double lotSize = riskAmount / (stopLossDistance / tickSize * tickValue);

   // Normalize lot size
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));

   return lotSize;
}

//+------------------------------------------------------------------+
//| Reset strategy to passive state                                 |
//+------------------------------------------------------------------+
void ResetStrategy()
{
   tradingPhase = PHASE_PASSIVE;
   fibHighPrice = 0;
   fibLowPrice = 0;
   fibLevel236 = 0;
   fibLevel500 = 0;
   firstFractalPrice = 0;
   secondFractalPrice = 0;
   firstFractalIsUp = false;
   secondFractalIsUp = false;
   firstFractalTime = 0;
   secondFractalTime = 0;
   longTicket = 0;
   shortTicket = 0;
   longEntryPrice = 0;
   shortEntryPrice = 0;
   waitingForNewBar = false;

   printf("Strategy reset to PASSIVE state");
}

//+------------------------------------------------------------------+
//| Log heartbeat information                                        |
//+------------------------------------------------------------------+
void LogHeartbeat()
{
   static datetime lastHeartbeat = 0;
   datetime currentTime = TimeCurrent();

   if(currentTime - lastHeartbeat >= 3600) // Log every hour
   {
      lastHeartbeat = currentTime;

      string phaseStr = "";
      switch(tradingPhase)
      {
         case PHASE_PASSIVE: phaseStr = "PASSIVE"; break;
         case PHASE_ACTIVE_LONG: phaseStr = "ACTIVE_LONG"; break;
         case PHASE_ACTIVE_SHORT: phaseStr = "ACTIVE_SHORT"; break;
      }

      printf("=== HEARTBEAT === Phase: %s | Fib Range: %.5f-%.5f | Levels: 236=%.5f, 500=%.5f | Positions: Long=%I64u, Short=%I64u",
             phaseStr, fibLowPrice, fibHighPrice, fibLevel236, fibLevel500, longTicket, shortTicket);
   }
}
