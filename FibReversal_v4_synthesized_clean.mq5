//+------------------------------------------------------------------+
//| FibReversal_v4_synthesized_clean.mq5                            |
//| Synthesized EA combining profitability of v3 with logic of v1   |
//| Based on onurFibo.py - 285 trades target                        |
//+------------------------------------------------------------------+
#property version     "4.00"
#property description "Synthesized Fibonacci Reversal EA - v3 profitability + v1 logic"

#define FibReversalMagic 12345

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Input parameters (matching Python script)
input double InpPriceChangePercentage = 3.0;    // MIN_YUZDE_FARK = 0.03 (3%)
input double InpFibonacciLevel = 0.236;         // Fibonacci 0.236 level
input double InpHardStopPercentage = 1.5;       // HARD_STOP_YUZDE = 0.015 (1.5%)
input double InpLotSize = 0.1;                  // Fixed lot size

//--- Trading phases (matching Python modes)
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE = 0,           // Not looking for any pattern
   PHASE_ACTIVE_LONG = 1,       // Looking for bullish fibonacci (Yükseliş Fibi Arıyor)
   PHASE_ACTIVE_SHORT = 2       // Looking for bearish fibonacci (Düşüş Fibi Arıyor)
};

//+------------------------------------------------------------------+
//| Fibonacci Reversal Expert Advisor class                         |
//+------------------------------------------------------------------+
class CFibReversalExpert
{
protected:
   // Trading objects
   double            m_adjusted_point;             // Point value adjusted for 3 or 5 digits
   CSymbolInfo       m_symbol;                     // Symbol info object
   CTrade            m_trade;                      // Trading object
   CPositionInfo     m_position;                   // Position info object
   CAccountInfo      m_account;                    // Account info wrapper

   // Trading state variables
   ENUM_TRADING_PHASE m_tradingPhase;
   double            m_fibHighPrice;
   double            m_fibLowPrice;
   double            m_fibLevel236;
   double            m_fibLevel500;
   datetime          m_fibHighTime;
   datetime          m_fibLowTime;

   // v1 style validation variables
   bool              m_candle4hClosed;
   bool              m_waitingForTouch;
   double            m_longEntryPrice;
   double            m_shortEntryPrice;

   // v3 style tracking variables (Python matching)
   double            m_kilitli_tepe_fiyat;
   datetime          m_kilitli_tepe_zaman;
   double            m_kilitli_dip_fiyat;
   datetime          m_kilitli_dip_zaman;

public:
                     CFibReversalExpert(void);
                    ~CFibReversalExpert(void);
   bool              Init(void);
   void              Deinit(void);
   bool              Processing(void);

protected:
   bool              DetectFractals(void);
   bool              ProcessActivePhase(void);
   bool              UpdatePeakTroughTracking(void);
   void              CalculateFibonacciLevels(void);
   bool              LongOpened(void);
   bool              ShortOpened(void);
   bool              LongClosed(void);
   bool              ShortClosed(void);
   void              ResetStrategy(void);
};

//--- Global expert
CFibReversalExpert ExtExpert;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   if(!ExtExpert.Init())
   {
      Print("Expert initialization failed");
      return(INIT_FAILED);
   }
   
   Print("FibReversal_v4_synthesized EA initialized - Target: 285 trades like Python script");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ExtExpert.Deinit();
   Print("FibReversal_v4_synthesized EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   ExtExpert.Processing();
}

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalExpert::CFibReversalExpert(void) : m_adjusted_point(0),
                                               m_tradingPhase(PHASE_PASSIVE),
                                               m_fibHighPrice(0),
                                               m_fibLowPrice(0),
                                               m_fibLevel236(0),
                                               m_fibLevel500(0),
                                               m_fibHighTime(0),
                                               m_fibLowTime(0),
                                               m_candle4hClosed(false),
                                               m_waitingForTouch(false),
                                               m_longEntryPrice(0),
                                               m_shortEntryPrice(0),
                                               m_kilitli_tepe_fiyat(0),
                                               m_kilitli_tepe_zaman(0),
                                               m_kilitli_dip_fiyat(DBL_MAX),
                                               m_kilitli_dip_zaman(0)
{
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalExpert::~CFibReversalExpert(void)
{
}

//+------------------------------------------------------------------+
//| Initialization and checking for input parameters                |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Init(void)
{
   if(!m_symbol.Name(Symbol()))
   {
      printf("Error setting symbol name");
      return(false);
   }
   
   if(!m_symbol.RefreshRates())
   {
      printf("Error refreshing rates");
      return(false);
   }
   
   m_adjusted_point = m_symbol.Point();
   if(m_symbol.Digits() == 3 || m_symbol.Digits() == 5)
      m_adjusted_point *= 10;
   
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());
   
   ResetStrategy();
   
   return(true);
}

//+------------------------------------------------------------------+
//| Deinitialization                                                 |
//+------------------------------------------------------------------+
void CFibReversalExpert::Deinit(void)
{
   // Clean up any resources if needed
}

//+------------------------------------------------------------------+
//| Main processing function                                         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Processing(void)
{
   if(!m_symbol.RefreshRates())
      return(false);
   
   // Process existing positions first (v3 style - direct Python matching)
   if(m_position.SelectByMagic(Symbol(), FibReversalMagic))
   {
      if(m_position.PositionType() == POSITION_TYPE_BUY)
      {
         if(!LongClosed())
            return(true); // Continue monitoring position
      }
      else if(m_position.PositionType() == POSITION_TYPE_SELL)
      {
         if(!ShortClosed())
            return(true); // Continue monitoring position
      }
   }
   
   // Detect fractals and update trading phase (v3 Python style)
   DetectFractals();
   
   // Process active phase (hybrid approach)
   ProcessActivePhase();
   
   return(true);
}

//+------------------------------------------------------------------+
//| Reset strategy to passive state                                 |
//+------------------------------------------------------------------+
void CFibReversalExpert::ResetStrategy(void)
{
   m_tradingPhase = PHASE_PASSIVE;
   m_fibHighPrice = 0;
   m_fibLowPrice = 0;
   m_fibLevel236 = 0;
   m_fibLevel500 = 0;
   m_fibHighTime = 0;
   m_fibLowTime = 0;
   
   // Reset v1 style variables
   m_candle4hClosed = false;
   m_waitingForTouch = false;
   m_longEntryPrice = 0;
   m_shortEntryPrice = 0;
   
   // Reset v3 style variables
   m_kilitli_tepe_fiyat = 0;
   m_kilitli_tepe_zaman = 0;
   m_kilitli_dip_fiyat = DBL_MAX;
   m_kilitli_dip_zaman = 0;
}

//+------------------------------------------------------------------+
//| Detect fractals - v3 Python style pattern detection            |
//+------------------------------------------------------------------+
bool CFibReversalExpert::DetectFractals(void)
{
   // Get last 3 closes for pattern detection (Python style)
   double close0 = iClose(NULL, PERIOD_H4, 2); // 3 bars ago
   double close1 = iClose(NULL, PERIOD_H4, 1); // 2 bars ago
   double close2 = iClose(NULL, PERIOD_H4, 0); // 1 bar ago (current completed)

   // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor'
   bool bullishPattern = (close0 > close1 && close1 > close2);

   // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor'
   bool bearishPattern = (close0 < close1 && close1 < close2);

   // v3 style: Only detect new patterns when NOT already in fibonacci tracking mode
   if(bullishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      printf("BULLISH PATTERN: close0 > close1 > close2 - switching to bullish fib mode");
      m_tradingPhase = PHASE_ACTIVE_LONG;

      // Python: kilitli_tepe_fiyat = df['High'].iloc[i-2:i+1].max()
      double high0 = iHigh(NULL, PERIOD_H4, 2);
      double high1 = iHigh(NULL, PERIOD_H4, 1);
      double high2 = iHigh(NULL, PERIOD_H4, 0);

      m_kilitli_tepe_fiyat = MathMax(high0, MathMax(high1, high2));
      m_kilitli_tepe_zaman = TimeCurrent();

      printf("Locked peak: %.5f at %s", m_kilitli_tepe_fiyat, TimeToString(m_kilitli_tepe_zaman));
      return(true);
   }

   if(bearishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      printf("BEARISH PATTERN: close0 < close1 < close2 - switching to bearish fib mode");
      m_tradingPhase = PHASE_ACTIVE_SHORT;

      // Python: kilitli_dip_fiyat = df['Low'].iloc[i-2:i+1].min()
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);

      m_kilitli_dip_fiyat = MathMin(low0, MathMin(low1, low2));
      m_kilitli_dip_zaman = TimeCurrent();

      printf("Locked trough: %.5f at %s", m_kilitli_dip_fiyat, TimeToString(m_kilitli_dip_zaman));
      return(true);
   }

   return(false);
}

//+------------------------------------------------------------------+
//| Process active phase - Hybrid v3 + v1 approach                |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessActivePhase(void)
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return(false);

   // Update peak/trough tracking (v3 Python style)
   UpdatePeakTroughTracking();

   // Try to open positions (hybrid approach)
   if(m_tradingPhase == PHASE_ACTIVE_LONG && m_fibLevel236 > 0)
   {
      return(LongOpened());
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT && m_fibLevel236 > 0)
   {
      return(ShortOpened());
   }

   return(false);
}

//+------------------------------------------------------------------+
//| Update peak/trough tracking - v3 Python style                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::UpdatePeakTroughTracking(void)
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return(false);

   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);
   datetime currentTime = TimeCurrent();

   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python: if row['High'] > kilitli_tepe_fiyat: kilitli_tepe_fiyat = row['High']
      if(currentHigh > m_kilitli_tepe_fiyat)
      {
         m_kilitli_tepe_fiyat = currentHigh;
         m_kilitli_tepe_zaman = currentTime;
         printf("Peak updated to %.5f (bullish mode)", m_kilitli_tepe_fiyat);
      }

      // Python: fark = (kilitli_tepe_fiyat - row['Low']) / row['Low']
      double fark = (m_kilitli_tepe_fiyat - currentLow) / currentLow * 100.0;
      if(fark >= InpPriceChangePercentage)
      {
         if(m_fibHighPrice != m_kilitli_tepe_fiyat || m_fibLowPrice != currentLow)
         {
            m_fibHighPrice = m_kilitli_tepe_fiyat;
            m_fibLowPrice = currentLow;
            m_fibHighTime = m_kilitli_tepe_zaman;
            m_fibLowTime = currentTime;

            printf("Bullish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, fark);
            CalculateFibonacciLevels();
            return(true);
         }
      }
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // Python: if row['Low'] < kilitli_dip_fiyat: kilitli_dip_fiyat = row['Low']
      if(currentLow < m_kilitli_dip_fiyat)
      {
         m_kilitli_dip_fiyat = currentLow;
         m_kilitli_dip_zaman = currentTime;
         printf("Trough updated to %.5f (bearish mode)", m_kilitli_dip_fiyat);
      }

      // Python: fark = (row['High'] - kilitli_dip_fiyat) / kilitli_dip_fiyat
      double fark = (currentHigh - m_kilitli_dip_fiyat) / m_kilitli_dip_fiyat * 100.0;
      if(fark >= InpPriceChangePercentage)
      {
         if(m_fibHighPrice != currentHigh || m_fibLowPrice != m_kilitli_dip_fiyat)
         {
            m_fibHighPrice = currentHigh;
            m_fibLowPrice = m_kilitli_dip_fiyat;
            m_fibHighTime = currentTime;
            m_fibLowTime = m_kilitli_dip_zaman;

            printf("Bearish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, fark);
            CalculateFibonacciLevels();
            return(true);
         }
      }
   }

   return(false);
}

//+------------------------------------------------------------------+
//| Calculate Fibonacci levels                                       |
//+------------------------------------------------------------------+
void CFibReversalExpert::CalculateFibonacciLevels(void)
{
   if(m_fibHighPrice == 0 || m_fibLowPrice == 0)
      return;

   double fibRange = m_fibHighPrice - m_fibLowPrice;

   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python: self.level_236 = self.dip_fiyat + 0.236 * diff
      // Python: self.level_500 = self.dip_fiyat + 0.5 * diff
      m_fibLevel236 = m_fibLowPrice + (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibLowPrice + (fibRange * 0.5);

      printf("LONG Fibonacci levels: 236=%.5f, 500=%.5f", m_fibLevel236, m_fibLevel500);
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // Python: self.level_236 = self.tepe_fiyat - 0.236 * diff
      // Python: self.level_500 = self.tepe_fiyat - 0.5 * diff
      m_fibLevel236 = m_fibHighPrice - (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibHighPrice - (fibRange * 0.5);

      printf("SHORT Fibonacci levels: 236=%.5f, 500=%.5f", m_fibLevel236, m_fibLevel500);
   }
}

//+------------------------------------------------------------------+
//| Open long position - Hybrid v3 + v1 approach                  |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongOpened(void)
{
   if(m_position.SelectByMagic(Symbol(), FibReversalMagic) &&
      m_position.PositionType() == POSITION_TYPE_BUY)
      return(false); // Already have long position

   if(m_tradingPhase != PHASE_ACTIVE_LONG || m_fibLevel236 == 0)
      return(false);

   // v3 style: Check if close price crosses above 236 level (immediate activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h >= m_fibLevel236)
   {
      // v1 style validation: Ensure 4H candle actually closed above level
      if(!m_candle4hClosed)
      {
         m_candle4hClosed = true;
         m_waitingForTouch = true;
         printf("4H candle closed above Fib 236 level: %.5f >= %.5f", close4h, m_fibLevel236);
         return(false); // Wait for pullback touch
      }

      // v1 style: Wait for price to touch 236 level from above (pullback)
      if(m_waitingForTouch)
      {
         double currentLow = iLow(NULL, PERIOD_H4, 0);
         if(currentLow <= m_fibLevel236 * 1.001) // Small tolerance for touch
         {
            printf("Price touched Fib 236 from above, opening long position");

            double ask = m_symbol.Ask();
            double hardStopDistance = ask * InpHardStopPercentage / 100.0;
            double stopLoss = ask - hardStopDistance;

            if(m_trade.Buy(InpLotSize, Symbol(), ask, stopLoss, 0, "FibRev_Long"))
            {
               m_longEntryPrice = ask;
               printf("Long position opened: Entry=%.5f, SL=%.5f, Fib236=%.5f, Fib500=%.5f",
                      ask, stopLoss, m_fibLevel236, m_fibLevel500);
               return(true);
            }
            else
            {
               printf("Failed to open long position. Error: %d", GetLastError());
               return(false);
            }
         }
      }
   }

   return(false);
}

//+------------------------------------------------------------------+
//| Open short position - Hybrid v3 + v1 approach                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortOpened(void)
{
   if(m_position.SelectByMagic(Symbol(), FibReversalMagic) &&
      m_position.PositionType() == POSITION_TYPE_SELL)
      return(false); // Already have short position

   if(m_tradingPhase != PHASE_ACTIVE_SHORT || m_fibLevel236 == 0)
      return(false);

   // v3 style: Check if close price crosses below 236 level (immediate activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h <= m_fibLevel236)
   {
      // v1 style validation: Ensure 4H candle actually closed below level
      if(!m_candle4hClosed)
      {
         m_candle4hClosed = true;
         m_waitingForTouch = true;
         printf("4H candle closed below Fib 236 level: %.5f <= %.5f", close4h, m_fibLevel236);
         return(false); // Wait for pullback touch
      }

      // v1 style: Wait for price to touch 236 level from below (pullback)
      if(m_waitingForTouch)
      {
         double currentHigh = iHigh(NULL, PERIOD_H4, 0);
         if(currentHigh >= m_fibLevel236 * 0.999) // Small tolerance for touch
         {
            printf("Price touched Fib 236 from below, opening short position");

            double bid = m_symbol.Bid();
            double hardStopDistance = bid * InpHardStopPercentage / 100.0;
            double stopLoss = bid + hardStopDistance;

            if(m_trade.Sell(InpLotSize, Symbol(), bid, stopLoss, 0, "FibRev_Short"))
            {
               m_shortEntryPrice = bid;
               printf("Short position opened: Entry=%.5f, SL=%.5f, Fib236=%.5f, Fib500=%.5f",
                      bid, stopLoss, m_fibLevel236, m_fibLevel500);
               return(true);
            }
            else
            {
               printf("Failed to open short position. Error: %d", GetLastError());
               return(false);
            }
         }
      }
   }

   return(false);
}

//+------------------------------------------------------------------+
//| Close long position - v3 Python style direct matching         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongClosed(void)
{
   if(!m_position.SelectByMagic(Symbol(), FibReversalMagic) ||
      m_position.PositionType() != POSITION_TYPE_BUY)
      return(true); // No long position

   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   // Python exit conditions:
   // 1. Take profit: row['High'] >= aktif_fib.level_500
   bool tpHit = (currentHigh >= m_fibLevel500);

   // 2. Hard stop: row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 - InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentLow < hardStopLevel);

   if(tpHit || hardStopHit)
   {
      if(m_trade.PositionClose(Symbol()))
      {
         string exitReason = tpHit ? "Take Profit (Fib 500)" : "Hard Stop";
         double profitPct = tpHit ?
            (m_fibLevel500 - m_fibLevel236) / m_fibLevel236 * 100.0 :
            -InpHardStopPercentage;

         printf("Long position closed: %s, Profit: %.2f%%", exitReason, profitPct);

         ResetStrategy();
         return(true);
      }
      else
      {
         printf("Failed to close long position. Error: %d", GetLastError());
         return(false);
      }
   }

   return(false);
}

//+------------------------------------------------------------------+
//| Close short position - v3 Python style direct matching        |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortClosed(void)
{
   if(!m_position.SelectByMagic(Symbol(), FibReversalMagic) ||
      m_position.PositionType() != POSITION_TYPE_SELL)
      return(true); // No short position

   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   // Python exit conditions:
   // 1. Take profit: row['Low'] <= aktif_fib.level_500
   bool tpHit = (currentLow <= m_fibLevel500);

   // 2. Hard stop: row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 + InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentHigh > hardStopLevel);

   if(tpHit || hardStopHit)
   {
      if(m_trade.PositionClose(Symbol()))
      {
         string exitReason = tpHit ? "Take Profit (Fib 500)" : "Hard Stop";
         double profitPct = tpHit ?
            (m_fibLevel236 - m_fibLevel500) / m_fibLevel236 * 100.0 :
            -InpHardStopPercentage;

         printf("Short position closed: %s, Profit: %.2f%%", exitReason, profitPct);

         ResetStrategy();
         return(true);
      }
      else
      {
         printf("Failed to close short position. Error: %d", GetLastError());
         return(false);
      }
   }

   return(false);
}
