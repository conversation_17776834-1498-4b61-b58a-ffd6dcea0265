//+------------------------------------------------------------------+
//|                                      FibReversal_v4_synthesized.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "4.00"

#include <Expert\Expert.mqh>
#include <Expert\Signal\SignalMA.mqh>
#include <Expert\Trailing\TrailingNone.mqh>
#include <Expert\Money\MoneyFixedRisk.mqh>

// Strategy parameters - matching onurFibo.py exactly
input double InpPriceChangePercentage = 3.0;          // MIN_YUZDE_FARK (3%)
input double InpFibonacciLevel       = 0.236;         // Fibonacci retracement level for entries
input double InpHardStopPercentage   = 1.5;           // HARD_STOP_YUZDE (1.5%)
input bool   InpUseFixedLotSize      = false;         // Use fixed lot size instead of risk percentage
input double InpFixedLotSize         = 0.01;          // Fixed lot size (when InpUseFixedLotSize = true)
input double InpRiskPercent          = 2.0;           // Risk percentage per trade (when InpUseFixedLotSize = false)

// Trading phases - matching Python script states
enum ENUM_TRADING_PHASE
{
   PHASE_SEARCHING_BULLISH,    // 'Yükseliş Fibi Arıyor'
   PHASE_SEARCHING_BEARISH,    // 'Düşüş Fibi Arıyor'
   PHASE_CANDIDATE_BULLISH,    // aday_fib for bullish
   PHASE_CANDIDATE_BEARISH,    // aday_fib for bearish
   PHASE_ACTIVE_LONG,          // aktif_fib for long
   PHASE_ACTIVE_SHORT          // aktif_fib for short
};

//+------------------------------------------------------------------+
//| CFibReversalExpert class                                         |
//+------------------------------------------------------------------+
class CFibReversalExpert : public CExpert
{
private:
   // Trading state variables
   ENUM_TRADING_PHASE m_tradingPhase;
   
   // Fibonacci levels
   double            m_fibHighPrice;        // kilitli_tepe_fiyat
   datetime          m_fibHighTime;         // kilitli_tepe_zaman
   double            m_fibLowPrice;         // kilitli_dip_fiyat
   datetime          m_fibLowTime;          // kilitli_dip_zaman
   double            m_fibLevel236;         // 0.236 level
   double            m_fibLevel500;         // 0.5 level
   
   // Position tracking
   ulong             m_longTicket;
   ulong             m_shortTicket;
   double            m_longEntryPrice;
   double            m_shortEntryPrice;
   
   // Fractal detection variables
   double            m_lastClose[3];        // For 3-candle fractal detection
   bool              m_fractalDetected;
   
   // Heartbeat logging
   datetime          m_lastHeartbeat;

public:
                     CFibReversalExpert(void);
                    ~CFibReversalExpert(void);
   virtual bool      InitCheckParameters(const int digits_adjust);
   virtual bool      InitIndicators(void);
   virtual void      OnTick(void);
   virtual void      OnTrade(void);
   virtual void      OnTimer(void);

protected:
   bool              ProcessSearchingPhase(void);
   bool              ProcessCandidatePhase(void);
   bool              ProcessActivePhase(void);
   bool              DetectFractals(void);
   bool              UpdatePeakTroughTracking(void);
   void              CalculateFibonacciLevels(void);
   bool              CheckCandidateValidation(void);
   bool              LongOpened(void);
   bool              ShortOpened(void);
   bool              LongClosed(void);
   bool              ShortClosed(void);
   double            CalculateLotSize(double stopLossDistance);
   void              ResetStrategy(void);
   void              LogHeartbeat(void);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalExpert::CFibReversalExpert(void) : m_tradingPhase(PHASE_SEARCHING_BULLISH),
                                                m_fibHighPrice(0),
                                                m_fibHighTime(0),
                                                m_fibLowPrice(DBL_MAX),
                                                m_fibLowTime(0),
                                                m_fibLevel236(0),
                                                m_fibLevel500(0),
                                                m_longTicket(0),
                                                m_shortTicket(0),
                                                m_longEntryPrice(0),
                                                m_shortEntryPrice(0),
                                                m_fractalDetected(false),
                                                m_lastHeartbeat(0)
{
   // Initialize close price array
   for(int i = 0; i < 3; i++)
      m_lastClose[i] = 0;
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalExpert::~CFibReversalExpert(void)
{
}

//+------------------------------------------------------------------+
//| Initialization and validation of the expert parameters          |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitCheckParameters(const int digits_adjust)
{
   if(!CExpert::InitCheckParameters(digits_adjust))
      return false;
      
   if(InpPriceChangePercentage <= 0)
   {
      printf("Error: Price change percentage must be positive");
      return false;
   }
   
   if(InpHardStopPercentage <= 0)
   {
      printf("Error: Hard stop percentage must be positive");
      return false;
   }
   
   if(InpUseFixedLotSize && InpFixedLotSize <= 0)
   {
      printf("Error: Fixed lot size must be positive");
      return false;
   }
   
   if(!InpUseFixedLotSize && InpRiskPercent <= 0)
   {
      printf("Error: Risk percentage must be positive");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Initialization of the indicators                                |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitIndicators(void)
{
   if(!CExpert::InitIndicators())
      return false;
      
   printf("FibReversal v4 Synthesized EA initialized successfully");
   printf("Parameters: PriceChange=%.1f%%, HardStop=%.1f%%, FibLevel=%.3f", 
          InpPriceChangePercentage, InpHardStopPercentage, InpFibonacciLevel);
   
   return true;
}

//+------------------------------------------------------------------+
//| Main processing function                                         |
//+------------------------------------------------------------------+
void CFibReversalExpert::OnTick(void)
{
   // Update heartbeat logging
   LogHeartbeat();
   
   // Detect fractals first (like Python script's 3-candle pattern detection)
   DetectFractals();
   
   // Process based on current trading phase
   switch(m_tradingPhase)
   {
      case PHASE_SEARCHING_BULLISH:
      case PHASE_SEARCHING_BEARISH:
         ProcessSearchingPhase();
         break;
         
      case PHASE_CANDIDATE_BULLISH:
      case PHASE_CANDIDATE_BEARISH:
         ProcessCandidatePhase();
         break;
         
      case PHASE_ACTIVE_LONG:
      case PHASE_ACTIVE_SHORT:
         ProcessActivePhase();
         break;
   }
}

//+------------------------------------------------------------------+
//| Trade event handler                                              |
//+------------------------------------------------------------------+
void CFibReversalExpert::OnTrade(void)
{
   // Handle trade events if needed
}

//+------------------------------------------------------------------+
//| Timer event handler                                              |
//+------------------------------------------------------------------+
void CFibReversalExpert::OnTimer(void)
{
   // Handle timer events if needed
}

//+------------------------------------------------------------------+
//| Global expert instance                                           |
//+------------------------------------------------------------------+
CFibReversalExpert g_expert;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   if(!g_expert.InitCheckParameters(0))
   {
      printf("Expert parameter validation failed");
      return INIT_PARAMETERS_INCORRECT;
   }

   if(!g_expert.InitIndicators())
   {
      printf("Expert indicator initialization failed");
      return INIT_FAILED;
   }

   printf("FibReversal v4 Synthesized EA initialized successfully");
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   printf("FibReversal v4 Synthesized EA deinitialized, reason: %d", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   g_expert.OnTick();
}

//+------------------------------------------------------------------+
//| Detect fractals using 3-candle pattern (like Python script)    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::DetectFractals(void)
{
   // Get the last 3 completed 4H candle closes
   double close0 = iClose(NULL, PERIOD_H4, 2); // i-2 in Python
   double close1 = iClose(NULL, PERIOD_H4, 1); // i-1 in Python
   double close2 = iClose(NULL, PERIOD_H4, 0); // i in Python (current completed)

   // Check if we have valid data
   if(close0 == 0 || close1 == 0 || close2 == 0)
      return false;

   // Python fractal detection logic:
   // if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor':
   if(close0 > close1 && close1 > close2 && m_tradingPhase != PHASE_SEARCHING_BULLISH)
   {
      printf("FRACTAL DETECTED: Bearish to Bullish switch (%.5f > %.5f > %.5f)", close0, close1, close2);
      m_tradingPhase = PHASE_SEARCHING_BULLISH;

      // Python: kilitli_tepe_fiyat = df['High'].iloc[i-2:i+1].max()
      double high0 = iHigh(NULL, PERIOD_H4, 2);
      double high1 = iHigh(NULL, PERIOD_H4, 1);
      double high2 = iHigh(NULL, PERIOD_H4, 0);

      m_fibHighPrice = MathMax(MathMax(high0, high1), high2);
      m_fibHighTime = iTime(NULL, PERIOD_H4, 0); // Current time

      printf("Peak locked at %.5f for bullish search", m_fibHighPrice);
      return true;
   }

   // elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor':
   if(close0 < close1 && close1 < close2 && m_tradingPhase != PHASE_SEARCHING_BEARISH)
   {
      printf("FRACTAL DETECTED: Bullish to Bearish switch (%.5f < %.5f < %.5f)", close0, close1, close2);
      m_tradingPhase = PHASE_SEARCHING_BEARISH;

      // Python: kilitli_dip_fiyat = df['Low'].iloc[i-2:i+1].min()
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);

      m_fibLowPrice = MathMin(MathMin(low0, low1), low2);
      m_fibLowTime = iTime(NULL, PERIOD_H4, 0); // Current time

      printf("Trough locked at %.5f for bearish search", m_fibLowPrice);
      return true;
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process searching phase (looking for fibonacci candidates)      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessSearchingPhase(void)
{
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);
   datetime currentTime = iTime(NULL, PERIOD_H4, 0);

   if(m_tradingPhase == PHASE_SEARCHING_BULLISH)
   {
      // Python: if row['High'] > kilitli_tepe_fiyat:
      if(currentHigh > m_fibHighPrice)
      {
         m_fibHighPrice = currentHigh;
         m_fibHighTime = currentTime;
         printf("Peak updated to %.5f (bullish search)", m_fibHighPrice);
      }

      // Check for fibonacci candidate creation
      // Python: fark = (kilitli_tepe_fiyat - row['Low']) / row['Low']
      double priceChange = (m_fibHighPrice - currentLow) / currentLow * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         printf("BULLISH CANDIDATE: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                m_fibHighPrice, currentLow, priceChange);

         // Create fibonacci candidate
         m_fibLowPrice = currentLow;
         m_fibLowTime = currentTime;
         CalculateFibonacciLevels();
         m_tradingPhase = PHASE_CANDIDATE_BULLISH;

         printf("Fibonacci candidate created - waiting for activation above %.5f", m_fibLevel236);
         return true;
      }
   }
   else if(m_tradingPhase == PHASE_SEARCHING_BEARISH)
   {
      // Python: if row['Low'] < kilitli_dip_fiyat:
      if(currentLow < m_fibLowPrice)
      {
         m_fibLowPrice = currentLow;
         m_fibLowTime = currentTime;
         printf("Trough updated to %.5f (bearish search)", m_fibLowPrice);
      }

      // Check for fibonacci candidate creation
      // Python: fark = (row['High'] - kilitli_dip_fiyat) / kilitli_dip_fiyat
      double priceChange = (currentHigh - m_fibLowPrice) / m_fibLowPrice * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         printf("BEARISH CANDIDATE: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                currentHigh, m_fibLowPrice, priceChange);

         // Create fibonacci candidate
         m_fibHighPrice = currentHigh;
         m_fibHighTime = currentTime;
         CalculateFibonacciLevels();
         m_tradingPhase = PHASE_CANDIDATE_BEARISH;

         printf("Fibonacci candidate created - waiting for activation below %.5f", m_fibLevel236);
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Calculate Fibonacci levels based on current high/low            |
//+------------------------------------------------------------------+
void CFibReversalExpert::CalculateFibonacciLevels(void)
{
   double fibRange = m_fibHighPrice - m_fibLowPrice;

   if(m_tradingPhase == PHASE_CANDIDATE_BULLISH || m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // For bullish: retracement UP from the low
      // Python: self.level_236 = self.dip_fiyat + 0.236 * diff
      // Python: self.level_500 = self.dip_fiyat + 0.5 * diff
      m_fibLevel236 = m_fibLowPrice + (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibLowPrice + (fibRange * 0.5);

      printf("BULLISH Fibonacci levels: 236=%.5f, 500=%.5f (Range: %.5f to %.5f)",
             m_fibLevel236, m_fibLevel500, m_fibLowPrice, m_fibHighPrice);
   }
   else if(m_tradingPhase == PHASE_CANDIDATE_BEARISH || m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // For bearish: retracement DOWN from the high
      // Python: self.level_236 = self.tepe_fiyat - 0.236 * diff
      // Python: self.level_500 = self.tepe_fiyat - 0.5 * diff
      m_fibLevel236 = m_fibHighPrice - (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibHighPrice - (fibRange * 0.5);

      printf("BEARISH Fibonacci levels: 236=%.5f, 500=%.5f (Range: %.5f to %.5f)",
             m_fibLevel236, m_fibLevel500, m_fibLowPrice, m_fibHighPrice);
   }
}

//+------------------------------------------------------------------+
//| Process candidate phase (aday_fib validation)                   |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessCandidatePhase(void)
{
   double currentClose = iClose(NULL, PERIOD_H4, 0);

   if(m_tradingPhase == PHASE_CANDIDATE_BULLISH)
   {
      // Python validation logic for bullish candidate:
      // if row['Close'] < aday_fib.dip_fiyat or row['Close'] > aday_fib.tepe_fiyat:
      if(currentClose < m_fibLowPrice || currentClose > m_fibHighPrice)
      {
         printf("BULLISH CANDIDATE INVALIDATED: Close %.5f outside range [%.5f, %.5f]",
                currentClose, m_fibLowPrice, m_fibHighPrice);
         m_tradingPhase = PHASE_SEARCHING_BULLISH;
         return false;
      }

      // elif row['Close'] > aday_fib.level_236:
      if(currentClose > m_fibLevel236)
      {
         printf("BULLISH ACTIVATION: Close %.5f > Fib 236 %.5f - activating long setup",
                currentClose, m_fibLevel236);
         m_tradingPhase = PHASE_ACTIVE_LONG;
         return LongOpened();
      }
   }
   else if(m_tradingPhase == PHASE_CANDIDATE_BEARISH)
   {
      // Python validation logic for bearish candidate:
      // if row['Close'] > aday_fib.tepe_fiyat or row['Close'] < aday_fib.dip_fiyat:
      if(currentClose > m_fibHighPrice || currentClose < m_fibLowPrice)
      {
         printf("BEARISH CANDIDATE INVALIDATED: Close %.5f outside range [%.5f, %.5f]",
                currentClose, m_fibLowPrice, m_fibHighPrice);
         m_tradingPhase = PHASE_SEARCHING_BEARISH;
         return false;
      }

      // elif row['Close'] < aday_fib.level_236:
      if(currentClose < m_fibLevel236)
      {
         printf("BEARISH ACTIVATION: Close %.5f < Fib 236 %.5f - activating short setup",
                currentClose, m_fibLevel236);
         m_tradingPhase = PHASE_ACTIVE_SHORT;
         return ShortOpened();
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process active phase (manage open positions)                    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessActivePhase(void)
{
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      return LongClosed();
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      return ShortClosed();
   }

   return false;
}

//+------------------------------------------------------------------+
//| Open long position (from v3_sharpe6's immediate entry)          |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongOpened(void)
{
   // Check if we already have a position
   if(m_longTicket != 0)
      return false;

   double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);

   // Calculate lot size based on hard stop distance
   double hardStopDistance = ask * InpHardStopPercentage / 100.0;
   double lotSize = CalculateLotSize(hardStopDistance);

   m_longEntryPrice = ask;

   printf("Opening long position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
          ask, m_fibLevel236, m_fibLevel500, lotSize);

   // Open position using MQL5 trade functions
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = Symbol();
   request.volume = lotSize;
   request.type = ORDER_TYPE_BUY;
   request.price = ask;
   request.deviation = 10;
   request.magic = 12345;
   request.comment = "FibReversal Long";

   if(OrderSend(request, result))
   {
      m_longTicket = result.order;
      printf("Long position opened successfully, ticket: %I64u", m_longTicket);
      return true;
   }
   else
   {
      printf("Error opening long position: %d", GetLastError());
      return false;
   }
}

//+------------------------------------------------------------------+
//| Open short position (from v3_sharpe6's immediate entry)         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortOpened(void)
{
   // Check if we already have a position
   if(m_shortTicket != 0)
      return false;

   double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);

   // Calculate lot size based on hard stop distance
   double hardStopDistance = bid * InpHardStopPercentage / 100.0;
   double lotSize = CalculateLotSize(hardStopDistance);

   m_shortEntryPrice = bid;

   printf("Opening short position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
          bid, m_fibLevel236, m_fibLevel500, lotSize);

   // Open position using MQL5 trade functions
   MqlTradeRequest request = {};
   MqlTradeResult result = {};

   request.action = TRADE_ACTION_DEAL;
   request.symbol = Symbol();
   request.volume = lotSize;
   request.type = ORDER_TYPE_SELL;
   request.price = bid;
   request.deviation = 10;
   request.magic = 12345;
   request.comment = "FibReversal Short";

   if(OrderSend(request, result))
   {
      m_shortTicket = result.order;
      printf("Short position opened successfully, ticket: %I64u", m_shortTicket);
      return true;
   }
   else
   {
      printf("Error opening short position: %d", GetLastError());
      return false;
   }
}

//+------------------------------------------------------------------+
//| Check long position exit conditions (matching Python script)    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongClosed(void)
{
   if(m_longTicket == 0)
      return false;

   // Get current bar data (like Python script)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   bool exitTriggered = false;
   string exitReason = "";

   // Python exit conditions for long positions:
   // 1. Take profit: row['High'] >= aktif_fib.level_500
   if(currentHigh >= m_fibLevel500)
   {
      exitTriggered = true;
      exitReason = "Take Profit (Fib 500)";
      printf("LONG TP HIT: High %.5f >= Fib500 %.5f", currentHigh, m_fibLevel500);
   }

   // 2. Hard stop: row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 - InpHardStopPercentage / 100.0);
   if(currentLow < hardStopLevel)
   {
      exitTriggered = true;
      exitReason = "Hard Stop";
      printf("LONG HARD STOP HIT: Low %.5f < HardStop %.5f", currentLow, hardStopLevel);
   }

   if(exitTriggered)
   {
      // Close the position
      MqlTradeRequest request = {};
      MqlTradeResult result = {};

      request.action = TRADE_ACTION_DEAL;
      request.symbol = Symbol();
      request.type = ORDER_TYPE_SELL;
      request.position = m_longTicket;
      request.price = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      request.deviation = 10;
      request.magic = 12345;
      request.comment = "Close Long: " + exitReason;

      if(OrderSend(request, result))
      {
         printf("Long position closed successfully: %s, ticket: %I64u", exitReason, m_longTicket);

         // Reset for next trade
         m_longTicket = 0;
         m_longEntryPrice = 0;
         m_tradingPhase = PHASE_SEARCHING_BULLISH;

         return true;
      }
      else
      {
         printf("Error closing long position: %d", GetLastError());
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check short position exit conditions (matching Python script)   |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortClosed(void)
{
   if(m_shortTicket == 0)
      return false;

   // Get current bar data (like Python script)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   bool exitTriggered = false;
   string exitReason = "";

   // Python exit conditions for short positions:
   // 1. Take profit: row['Low'] <= aktif_fib.level_500
   if(currentLow <= m_fibLevel500)
   {
      exitTriggered = true;
      exitReason = "Take Profit (Fib 500)";
      printf("SHORT TP HIT: Low %.5f <= Fib500 %.5f", currentLow, m_fibLevel500);
   }

   // 2. Hard stop: row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 + InpHardStopPercentage / 100.0);
   if(currentHigh > hardStopLevel)
   {
      exitTriggered = true;
      exitReason = "Hard Stop";
      printf("SHORT HARD STOP HIT: High %.5f > HardStop %.5f", currentHigh, hardStopLevel);
   }

   if(exitTriggered)
   {
      // Close the position
      MqlTradeRequest request = {};
      MqlTradeResult result = {};

      request.action = TRADE_ACTION_DEAL;
      request.symbol = Symbol();
      request.type = ORDER_TYPE_BUY;
      request.position = m_shortTicket;
      request.price = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      request.deviation = 10;
      request.magic = 12345;
      request.comment = "Close Short: " + exitReason;

      if(OrderSend(request, result))
      {
         printf("Short position closed successfully: %s, ticket: %I64u", exitReason, m_shortTicket);

         // Reset for next trade
         m_shortTicket = 0;
         m_shortEntryPrice = 0;
         m_tradingPhase = PHASE_SEARCHING_BEARISH;

         return true;
      }
      else
      {
         printf("Error closing short position: %d", GetLastError());
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CFibReversalExpert::CalculateLotSize(double stopLossDistance)
{
   if(InpUseFixedLotSize)
   {
      return InpFixedLotSize;
   }

   // Calculate lot size based on risk percentage
   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpRiskPercent / 100.0;

   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

   if(tickValue == 0 || tickSize == 0 || stopLossDistance == 0)
      return InpFixedLotSize;

   double lotSize = riskAmount / (stopLossDistance / tickSize * tickValue);

   // Apply lot size limits
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));
   lotSize = MathFloor(lotSize / lotStep) * lotStep;

   return lotSize;
}

//+------------------------------------------------------------------+
//| Reset strategy state                                             |
//+------------------------------------------------------------------+
void CFibReversalExpert::ResetStrategy(void)
{
   m_tradingPhase = PHASE_SEARCHING_BULLISH;
   m_fibHighPrice = 0;
   m_fibHighTime = 0;
   m_fibLowPrice = DBL_MAX;
   m_fibLowTime = 0;
   m_fibLevel236 = 0;
   m_fibLevel500 = 0;
   m_longTicket = 0;
   m_shortTicket = 0;
   m_longEntryPrice = 0;
   m_shortEntryPrice = 0;
   m_fractalDetected = false;

   printf("Strategy state reset");
}

//+------------------------------------------------------------------+
//| Log heartbeat information                                        |
//+------------------------------------------------------------------+
void CFibReversalExpert::LogHeartbeat(void)
{
   datetime currentTime = TimeCurrent();

   // Log heartbeat every hour
   if(currentTime - m_lastHeartbeat >= 3600)
   {
      m_lastHeartbeat = currentTime;

      string phaseStr = "";
      switch(m_tradingPhase)
      {
         case PHASE_SEARCHING_BULLISH: phaseStr = "Searching Bullish"; break;
         case PHASE_SEARCHING_BEARISH: phaseStr = "Searching Bearish"; break;
         case PHASE_CANDIDATE_BULLISH: phaseStr = "Candidate Bullish"; break;
         case PHASE_CANDIDATE_BEARISH: phaseStr = "Candidate Bearish"; break;
         case PHASE_ACTIVE_LONG: phaseStr = "Active Long"; break;
         case PHASE_ACTIVE_SHORT: phaseStr = "Active Short"; break;
      }

      printf("HEARTBEAT: Phase=%s, FibHigh=%.5f, FibLow=%.5f, Fib236=%.5f, Fib500=%.5f",
             phaseStr, m_fibHighPrice, m_fibLowPrice, m_fibLevel236, m_fibLevel500);
   }
}

//+------------------------------------------------------------------+
//| Check candidate validation (placeholder for future use)         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::CheckCandidateValidation(void)
{
   // This function can be used for additional validation logic if needed
   return true;
}

//+------------------------------------------------------------------+
//| Update peak/trough tracking (placeholder for future use)        |
//+------------------------------------------------------------------+
bool CFibReversalExpert::UpdatePeakTroughTracking(void)
{
   // This function can be used for additional peak/trough logic if needed
   return true;
}
