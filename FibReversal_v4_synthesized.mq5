//+------------------------------------------------------------------+
//| FibReversal_v4_synthesized.mq5                                  |
//| Synthesized EA combining profitability of v3 with logic of v1   |
//| Based on onurFibo.py - Fibonacci Retracement Trading Strategy   |
//+------------------------------------------------------------------+
#property copyright "Synthesized from v1 and v3"
#property version   "4.00"

#include <Expert\Expert.mqh>
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Strategy parameters - Using v3's profitable 3% threshold
input double InpPriceChangePercentage = 3.0;          // Price change threshold percentage (v3's profitable setting)
input double InpFibonacciLevel       = 0.236;         // Fibonacci retracement level for entries
input double InpHardStopPercentage   = 1.5;           // Hard stop percentage
input bool   InpUseFixedLotSize      = false;         // Use fixed lot size instead of risk percentage
input double InpFixedLotSize         = 0.01;          // Fixed lot size (when InpUseFixedLotSize = true)
input double InpRiskPercent          = 2.0;           // Risk percentage per trade (when InpUseFixedLotSize = false)

// Trading phases
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE,      // Looking for pattern signals
   PHASE_ACTIVE_LONG,  // Looking for bullish fibonacci setup
   PHASE_ACTIVE_SHORT  // Looking for bearish fibonacci setup
};

//+------------------------------------------------------------------+
//| CFibReversalExpert class - Synthesized approach                 |
//+------------------------------------------------------------------+
class CFibReversalExpert : public CExpert
{
protected:
   // Trading objects - using base class members (m_trade, m_symbol, m_position, m_account)

   // Indicator handles
   int               m_handle_fractals;

   // Tick data
   MqlTick           m_tick;

   // Strategy state variables
   ENUM_TRADING_PHASE m_tradingPhase;
   
   // Fibonacci range tracking (v3 style)
   double            m_fibHighPrice;
   double            m_fibLowPrice;
   double            m_fibLevel236;
   double            m_fibLevel500;
   
   // Fractal tracking (v1 style for validation)
   double            m_firstFractalPrice;
   double            m_secondFractalPrice;
   bool              m_firstFractalIsUp;
   bool              m_secondFractalIsUp;
   datetime          m_firstFractalTime;
   datetime          m_secondFractalTime;
   
   // Position tracking
   ulong             m_longTicket;
   ulong             m_shortTicket;
   double            m_longEntryPrice;
   double            m_shortEntryPrice;
   
   // Bar tracking for pattern detection
   datetime          m_lastProcessedBarTime;
   bool              m_waitingForNewBar;

public:
                     CFibReversalExpert(void);
                    ~CFibReversalExpert(void);
   virtual bool      Init(void);
   virtual void      Deinit(void);
   virtual bool      Processing(void);

protected:
   bool              InitCheckParameters(const int digits_adjust);
   bool              InitIndicators(void);
   bool              ProcessPassivePhase(void);
   bool              ProcessActivePhase(void);
   bool              DetectPatternsHybrid(void);
   bool              ValidateWithFractals(void);
   bool              UpdatePeakTroughTracking(void);
   void              CalculateFibonacciLevels(void);
   bool              ProcessNewFractal(double price, bool isUp, datetime time);
   bool              LongOpened(void);
   bool              ShortOpened(void);
   bool              LongClosed(void);
   bool              ShortClosed(void);
   double            CalculateLotSize(double stopLossDistance);
   void              ResetStrategy(void);

public:
   void              LogHeartbeat(void);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalExpert::CFibReversalExpert(void) : m_handle_fractals(INVALID_HANDLE),
                                                m_tradingPhase(PHASE_PASSIVE),
                                                m_fibHighPrice(0),
                                                m_fibLowPrice(0),
                                                m_fibLevel236(0),
                                                m_fibLevel500(0),
                                                m_firstFractalPrice(0),
                                                m_secondFractalPrice(0),
                                                m_firstFractalIsUp(false),
                                                m_secondFractalIsUp(false),
                                                m_firstFractalTime(0),
                                                m_secondFractalTime(0),
                                                m_longTicket(0),
                                                m_shortTicket(0),
                                                m_longEntryPrice(0),
                                                m_shortEntryPrice(0),
                                                m_lastProcessedBarTime(0),
                                                m_waitingForNewBar(false)
{
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalExpert::~CFibReversalExpert(void)
{
}

//+------------------------------------------------------------------+
//| Initialization function                                          |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Init(void)
{
   // Initialize base class first
   if(!CExpert::Init())
      return(false);

   if(!InitCheckParameters(m_symbol.Digits()))
      return(false);

   if(!InitIndicators())
      return(false);

   m_trade.SetExpertMagicNumber(12345);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());

   printf("=== FibReversal v4 Synthesized EA Initialized ===");
   printf("Price Change Threshold: %.1f%% (v3's profitable setting)", InpPriceChangePercentage);
   printf("Fibonacci Entry Level: %.3f", InpFibonacciLevel);
   printf("Hard Stop Percentage: %.1f%%", InpHardStopPercentage);
   printf("Using hybrid approach: v3's pattern detection + v1's fractal validation");

   return(true);
}

//+------------------------------------------------------------------+
//| Deinitialization function                                       |
//+------------------------------------------------------------------+
void CFibReversalExpert::Deinit(void)
{
   if(m_handle_fractals != INVALID_HANDLE)
      IndicatorRelease(m_handle_fractals);
}

//+------------------------------------------------------------------+
//| Main processing function                                         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Processing(void)
{
   // Refresh symbol rates
   SymbolInfoTick(Symbol(), m_tick);

   // Process based on current trading phase
   if(m_tradingPhase == PHASE_PASSIVE)
   {
      return ProcessPassivePhase();
   }
   else
   {
      return ProcessActivePhase();
   }
}

//+------------------------------------------------------------------+
//| Check input parameters                                           |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitCheckParameters(const int digits_adjust)
{
   if(InpRiskPercent <= 0 || InpRiskPercent > 10)
   {
      printf("Risk percentage must be between 0 and 10, current value: %f", InpRiskPercent);
      return false;
   }

   if(InpUseFixedLotSize && InpFixedLotSize <= 0)
   {
      printf("Fixed lot size must be greater than 0, current value: %f", InpFixedLotSize);
      return false;
   }

   if(InpPriceChangePercentage <= 0)
   {
      printf("Price change percentage must be greater than 0, current value: %f", InpPriceChangePercentage);
      return false;
   }

   if(InpFibonacciLevel <= 0 || InpFibonacciLevel >= 1)
   {
      printf("Fibonacci level must be between 0 and 1, current value: %f", InpFibonacciLevel);
      return false;
   }

   if(InpHardStopPercentage <= 0)
   {
      printf("Hard stop percentage must be greater than 0, current value: %f", InpHardStopPercentage);
      return false;
   }

   return true;
}

//+------------------------------------------------------------------+
//| Initialize indicators                                            |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitIndicators(void)
{
   // Create Fractals indicator on 4H timeframe (for validation)
   if(m_handle_fractals == INVALID_HANDLE)
      if((m_handle_fractals = iFractals(NULL, PERIOD_H4)) == INVALID_HANDLE)
      {
         printf("Error creating Fractals indicator on 4H timeframe");
         return false;
      }

   printf("DEBUG: Fractals indicator created successfully on 4H timeframe, handle: %d", m_handle_fractals);
   Sleep(1000); // Wait for indicator to calculate initial values

   return true;
}

//+------------------------------------------------------------------+
//| Process passive phase - Hybrid pattern detection               |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessPassivePhase(void)
{
   if(m_tradingPhase != PHASE_PASSIVE)
      return false;

   // Check if we're waiting for a new 4H bar
   if(m_waitingForNewBar)
   {
      datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
      if(currentBarTime != m_lastProcessedBarTime)
      {
         m_waitingForNewBar = false;
         m_lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - ready to start pattern detection");
      }
      else
      {
         return false;
      }
   }

   return DetectPatternsHybrid();
}

//+------------------------------------------------------------------+
//| Hybrid pattern detection - v3's 3-bar + v1's fractal validation|
//+------------------------------------------------------------------+
bool CFibReversalExpert::DetectPatternsHybrid(void)
{
   // Check if we need to wait for a new 4H bar
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
   if(m_waitingForNewBar)
   {
      if(currentBarTime == m_lastProcessedBarTime)
         return false;
      else
      {
         m_waitingForNewBar = false;
         m_lastProcessedBarTime = currentBarTime;
         printf("New 4H bar formed - starting hybrid pattern detection");
      }
   }

   // STEP 1: v3's Python-style 3-bar close pattern detection
   double close0 = iClose(NULL, PERIOD_H4, 2); // 2 bars ago
   double close1 = iClose(NULL, PERIOD_H4, 1); // 1 bar ago
   double close2 = iClose(NULL, PERIOD_H4, 0); // Current bar

   printf("DEBUG: 3-bar pattern check - close0=%.5f, close1=%.5f, close2=%.5f", close0, close1, close2);

   // Check for mode switching patterns (Python logic)
   bool bullishPattern = (close0 > close1 && close1 > close2); // Bearish trend → look for bullish fib
   bool bearishPattern = (close0 < close1 && close1 < close2); // Bullish trend → look for bearish fib

   if(bullishPattern)
   {
      printf("BULLISH PATTERN DETECTED: close0 > close1 > close2 - validating with fractals");

      // STEP 2: Validate with fractal detection (v1 style)
      if(ValidateWithFractals())
      {
         m_tradingPhase = PHASE_ACTIVE_LONG;

         // Initialize peak from last 3 bars (v3 style)
         double high0 = iHigh(NULL, PERIOD_H4, 2);
         double high1 = iHigh(NULL, PERIOD_H4, 1);
         double high2 = iHigh(NULL, PERIOD_H4, 0);
         m_fibHighPrice = MathMax(high0, MathMax(high1, high2));

         printf("BULLISH MODE ACTIVATED: Initial peak locked at %.5f", m_fibHighPrice);
         m_waitingForNewBar = true;
         return true;
      }
   }
   else if(bearishPattern)
   {
      printf("BEARISH PATTERN DETECTED: close0 < close1 < close2 - validating with fractals");

      // STEP 2: Validate with fractal detection (v1 style)
      if(ValidateWithFractals())
      {
         m_tradingPhase = PHASE_ACTIVE_SHORT;

         // Initialize trough from last 3 bars (v3 style)
         double low0 = iLow(NULL, PERIOD_H4, 2);
         double low1 = iLow(NULL, PERIOD_H4, 1);
         double low2 = iLow(NULL, PERIOD_H4, 0);
         m_fibLowPrice = MathMin(low0, MathMin(low1, low2));

         printf("BEARISH MODE ACTIVATED: Initial trough locked at %.5f", m_fibLowPrice);
         m_waitingForNewBar = true;
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Validate pattern with fractal detection (v1 approach)          |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ValidateWithFractals(void)
{
   // Get recent fractal data
   double upFractals[6];
   double downFractals[6];

   if(CopyBuffer(m_handle_fractals, 0, 0, 6, upFractals) != 6)
      return false;
   if(CopyBuffer(m_handle_fractals, 1, 0, 6, downFractals) != 6)
      return false;

   // Look for recent fractals to validate the pattern
   for(int i = 2; i < 5; i++)
   {
      bool upFractal = (upFractals[i] != EMPTY_VALUE && upFractals[i] != 0);
      bool downFractal = (downFractals[i] != EMPTY_VALUE && downFractals[i] != 0);

      if(upFractal || downFractal)
      {
         double fractalPrice = upFractal ? upFractals[i] : downFractals[i];
         bool fractalIsUp = upFractal;
         datetime fractalTime = iTime(NULL, PERIOD_H4, i);

         printf("DEBUG: Fractal validation - %s fractal at %.5f", fractalIsUp ? "UP" : "DOWN", fractalPrice);

         // Process the fractal using v1's logic
         bool fractalProcessed = ProcessNewFractal(fractalPrice, fractalIsUp, fractalTime);

         if(fractalProcessed)
         {
            // Check if we have a valid fractal couple with sufficient range
            if(m_firstFractalPrice != 0 && m_secondFractalPrice != 0)
            {
               double priceChange = MathAbs(m_firstFractalPrice - m_secondFractalPrice) / MathMin(m_firstFractalPrice, m_secondFractalPrice) * 100.0;

               if(priceChange >= InpPriceChangePercentage)
               {
                  printf("Fractal validation PASSED: %.2f%% range (threshold: %.2f%%)", priceChange, InpPriceChangePercentage);
                  return true;
               }
               else
               {
                  printf("Fractal validation FAILED: %.2f%% range below threshold %.2f%%", priceChange, InpPriceChangePercentage);
               }
            }
         }
      }
   }

   printf("Fractal validation FAILED: No valid fractal couple found");
   return false;
}

//+------------------------------------------------------------------+
//| Process new fractal (v1 logic)                                  |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessNewFractal(double price, bool isUp, datetime time)
{
   bool fractalUpdated = false;

   if(m_firstFractalPrice == 0) // No first fractal yet
   {
      m_firstFractalPrice = price;
      m_firstFractalIsUp = isUp;
      m_firstFractalTime = time;
      printf("First fractal set: %s at %.5f", isUp ? "UP" : "DOWN", price);
      fractalUpdated = true;
   }
   else if(m_secondFractalPrice == 0) // Have first, need second
   {
      if(isUp != m_firstFractalIsUp) // Opposite direction - potential second fractal
      {
         m_secondFractalPrice = price;
         m_secondFractalIsUp = isUp;
         m_secondFractalTime = time;
         printf("Second fractal set: %s at %.5f", isUp ? "UP" : "DOWN", price);
         fractalUpdated = true;
      }
      else // Same direction - update first fractal if more extreme
      {
         if((isUp && price > m_firstFractalPrice) || (!isUp && price < m_firstFractalPrice))
         {
            m_firstFractalPrice = price;
            m_firstFractalTime = time;
            printf("First fractal updated to more extreme: %s at %.5f", isUp ? "UP" : "DOWN", price);
            fractalUpdated = true;
         }
      }
   }
   else // Have both fractals - update the appropriate one if more extreme
   {
      double currentHigh = MathMax(m_firstFractalPrice, m_secondFractalPrice);
      double currentLow = MathMin(m_firstFractalPrice, m_secondFractalPrice);

      if(isUp && price > currentHigh) // New higher high
      {
         if(m_firstFractalPrice >= m_secondFractalPrice)
         {
            m_firstFractalPrice = price;
            m_firstFractalTime = time;
            m_firstFractalIsUp = isUp;
         }
         else
         {
            m_secondFractalPrice = price;
            m_secondFractalTime = time;
            m_secondFractalIsUp = isUp;
         }
         printf("Updated high fractal to %.5f", price);
         fractalUpdated = true;
      }
      else if(!isUp && price < currentLow) // New lower low
      {
         if(m_firstFractalPrice <= m_secondFractalPrice)
         {
            m_firstFractalPrice = price;
            m_firstFractalTime = time;
            m_firstFractalIsUp = isUp;
         }
         else
         {
            m_secondFractalPrice = price;
            m_secondFractalTime = time;
            m_secondFractalIsUp = isUp;
         }
         printf("Updated low fractal to %.5f", price);
         fractalUpdated = true;
      }
   }

   return fractalUpdated;
}

//+------------------------------------------------------------------+
//| Process active phase - Handle fibonacci setups                  |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ProcessActivePhase(void)
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;

   // Check for existing positions first
   if(m_longTicket != 0)
   {
      if(!LongClosed())
         return true; // Position still active
      else
      {
         // Position closed, reset and return to passive
         ResetStrategy();
         return false;
      }
   }

   if(m_shortTicket != 0)
   {
      if(!ShortClosed())
         return true; // Position still active
      else
      {
         // Position closed, reset and return to passive
         ResetStrategy();
         return false;
      }
   }

   // Update peak/trough tracking (v3 style with v1 validation)
   UpdatePeakTroughTracking();

   // Try to open positions based on fibonacci levels
   if(m_tradingPhase == PHASE_ACTIVE_LONG && m_fibLevel236 > 0)
   {
      return LongOpened();
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT && m_fibLevel236 > 0)
   {
      return ShortOpened();
   }

   return false;
}

//+------------------------------------------------------------------+
//| Update peak/trough tracking - Hybrid v3 + v1 approach         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::UpdatePeakTroughTracking(void)
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;

   bool rangeUpdated = false;
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python style reset logic: Only reset if fibonacci candidate exists AND price moves outside range
      double currentClose = iClose(NULL, PERIOD_H4, 0);
      if(m_fibLevel236 > 0 && (currentClose < m_fibLowPrice || currentClose > m_fibHighPrice))
      {
         printf("RESET: Close %.5f outside range [%.5f - %.5f] with active fib - returning to passive",
                currentClose, m_fibLowPrice, m_fibHighPrice);
         ResetStrategy();
         return rangeUpdated;
      }

      // v3 style: Continuously update peak like Python script
      if(currentHigh > m_fibHighPrice)
      {
         m_fibHighPrice = currentHigh;
         printf("Peak updated to %.5f (bullish mode)", m_fibHighPrice);
      }

      // Check if we meet the minimum range requirement using current peak and current low
      double priceChange = (m_fibHighPrice - currentLow) / currentLow * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(m_fibLowPrice != currentLow || m_fibLevel236 == 0)
         {
            m_fibLowPrice = currentLow;
            printf("Bullish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange);
            CalculateFibonacciLevels();
            rangeUpdated = true;
         }
      }
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // Python style reset logic: Only reset if fibonacci candidate exists AND price moves outside range
      double currentClose = iClose(NULL, PERIOD_H4, 0);
      if(m_fibLevel236 > 0 && (currentClose > m_fibHighPrice || currentClose < m_fibLowPrice))
      {
         printf("RESET: Close %.5f outside range [%.5f - %.5f] with active fib - returning to passive",
                currentClose, m_fibLowPrice, m_fibHighPrice);
         ResetStrategy();
         return rangeUpdated;
      }

      // v3 style: Continuously update trough like Python script
      if(currentLow < m_fibLowPrice)
      {
         m_fibLowPrice = currentLow;
         printf("Trough updated to %.5f (bearish mode)", m_fibLowPrice);
      }

      // Check if we meet the minimum range requirement using current high and current trough
      double priceChange = (currentHigh - m_fibLowPrice) / m_fibLowPrice * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(m_fibHighPrice != currentHigh || m_fibLevel236 == 0)
         {
            m_fibHighPrice = currentHigh;
            printf("Bearish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange);
            CalculateFibonacciLevels();
            rangeUpdated = true;
         }
      }
   }

   return rangeUpdated;
}

//+------------------------------------------------------------------+
//| Calculate Fibonacci levels (same logic as both v1 and v3)      |
//+------------------------------------------------------------------+
void CFibReversalExpert::CalculateFibonacciLevels(void)
{
   if(m_fibHighPrice == 0 || m_fibLowPrice == 0)
   {
      printf("ERROR: Cannot calculate Fibonacci levels - invalid range");
      return;
   }

   double fibRange = m_fibHighPrice - m_fibLowPrice;

   // Calculate Fibonacci retracement levels based on trading phase
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // For LONG setups (after price dropped): retracement UP from the low
      m_fibLevel236 = m_fibLowPrice + (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibLowPrice + (fibRange * 0.5);
      printf("LONG setup - Fibonacci levels calculated (retracement UP from low):");
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // For SHORT setups (after price rose): retracement DOWN from the high
      m_fibLevel236 = m_fibHighPrice - (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibHighPrice - (fibRange * 0.5);
      printf("SHORT setup - Fibonacci levels calculated (retracement DOWN from high):");
   }
   else
   {
      printf("ERROR: CalculateFibonacciLevels called in PASSIVE phase");
      return;
   }

   printf("  Range: %.5f (High: %.5f - Low: %.5f)", fibRange, m_fibHighPrice, m_fibLowPrice);
   printf("  Fib 0.236 (%.3f): %.5f", InpFibonacciLevel, m_fibLevel236);
   printf("  Fib 0.500: %.5f", m_fibLevel500);
   printf("  Current Bid: %.5f, Ask: %.5f", SymbolInfoDouble(Symbol(), SYMBOL_BID), SymbolInfoDouble(Symbol(), SYMBOL_ASK));
}

//+------------------------------------------------------------------+
//| Check for long position opening (v3 style)                     |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongOpened(void)
{
   if(m_longTicket != 0 || m_tradingPhase != PHASE_ACTIVE_LONG || m_fibLevel236 == 0)
      return false;

   // Check if 4H close is above fibonacci 236 level (Python style activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h > m_fibLevel236)
   {
      printf("BULLISH ACTIVATION: Close %.5f > Fib 236 %.5f - opening long position", close4h, m_fibLevel236);

      double ask = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
      double hardStopDistance = ask * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_longEntryPrice = ask;

      printf("Opening long position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             ask, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Buy(lotSize, Symbol(), ask))
      {
         m_longTicket = m_trade.ResultOrder();
         printf("Long position opened successfully, ticket: %I64u", m_longTicket);
         return true;
      }
      else
      {
         printf("Error opening long position: %s", m_trade.ResultComment());
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check for short position opening (v3 style)                    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortOpened(void)
{
   if(m_shortTicket != 0 || m_tradingPhase != PHASE_ACTIVE_SHORT || m_fibLevel236 == 0)
      return false;

   // Check if 4H close is below fibonacci 236 level (Python style activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h < m_fibLevel236)
   {
      printf("BEARISH ACTIVATION: Close %.5f < Fib 236 %.5f - opening short position", close4h, m_fibLevel236);

      double bid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
      double hardStopDistance = bid * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_shortEntryPrice = bid;

      printf("Opening short position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             bid, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Sell(lotSize, Symbol(), bid))
      {
         m_shortTicket = m_trade.ResultOrder();
         printf("Short position opened successfully, ticket: %I64u", m_shortTicket);
         return true;
      }
      else
      {
         printf("Error opening short position: %s", m_trade.ResultComment());
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Check if long position should be closed (Python style)         |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongClosed(void)
{
   if(m_longTicket == 0)
      return true;

   if(!m_position.SelectByTicket(m_longTicket))
   {
      printf("Long position ticket %I64u not found - assuming closed", m_longTicket);
      m_longTicket = 0;
      return true;
   }

   double currentBid = SymbolInfoDouble(Symbol(), SYMBOL_BID);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   // Python style exit conditions
   bool hardStopHit = (currentLow < m_fibLevel236 * (1 - InpHardStopPercentage / 100.0));
   bool targetReached = (currentBid >= m_fibLevel500);

   if(hardStopHit)
   {
      printf("LONG HARD STOP: Low %.5f < Stop %.5f - closing position",
             currentLow, m_fibLevel236 * (1 - InpHardStopPercentage / 100.0));

      if(m_trade.PositionClose(m_longTicket))
      {
         printf("Long position closed by hard stop, ticket: %I64u", m_longTicket);
         m_longTicket = 0;
         return true;
      }
   }
   else if(targetReached)
   {
      printf("LONG TARGET: Bid %.5f >= Target %.5f - closing position", currentBid, m_fibLevel500);

      if(m_trade.PositionClose(m_longTicket))
      {
         printf("Long position closed at target, ticket: %I64u", m_longTicket);
         m_longTicket = 0;
         return true;
      }
   }

   return false; // Position still active
}

//+------------------------------------------------------------------+
//| Check if short position should be closed (Python style)        |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortClosed(void)
{
   if(m_shortTicket == 0)
      return true;

   if(!m_position.SelectByTicket(m_shortTicket))
   {
      printf("Short position ticket %I64u not found - assuming closed", m_shortTicket);
      m_shortTicket = 0;
      return true;
   }

   double currentAsk = SymbolInfoDouble(Symbol(), SYMBOL_ASK);
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);

   // Python style exit conditions
   bool hardStopHit = (currentHigh > m_fibLevel236 * (1 + InpHardStopPercentage / 100.0));
   bool targetReached = (currentAsk <= m_fibLevel500);

   if(hardStopHit)
   {
      printf("SHORT HARD STOP: High %.5f > Stop %.5f - closing position",
             currentHigh, m_fibLevel236 * (1 + InpHardStopPercentage / 100.0));

      if(m_trade.PositionClose(m_shortTicket))
      {
         printf("Short position closed by hard stop, ticket: %I64u", m_shortTicket);
         m_shortTicket = 0;
         return true;
      }
   }
   else if(targetReached)
   {
      printf("SHORT TARGET: Ask %.5f <= Target %.5f - closing position", currentAsk, m_fibLevel500);

      if(m_trade.PositionClose(m_shortTicket))
      {
         printf("Short position closed at target, ticket: %I64u", m_shortTicket);
         m_shortTicket = 0;
         return true;
      }
   }

   return false; // Position still active
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk management                     |
//+------------------------------------------------------------------+
double CFibReversalExpert::CalculateLotSize(double stopLossDistance)
{
   if(InpUseFixedLotSize)
      return InpFixedLotSize;

   double accountBalance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = accountBalance * InpRiskPercent / 100.0;
   double tickValue = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_VALUE);
   double tickSize = SymbolInfoDouble(Symbol(), SYMBOL_TRADE_TICK_SIZE);

   if(tickValue == 0 || tickSize == 0 || stopLossDistance == 0)
      return InpFixedLotSize;

   double lotSize = riskAmount / (stopLossDistance / tickSize * tickValue);

   // Normalize lot size
   double minLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MIN);
   double maxLot = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_MAX);
   double lotStep = SymbolInfoDouble(Symbol(), SYMBOL_VOLUME_STEP);

   lotSize = MathMax(minLot, MathMin(maxLot, MathRound(lotSize / lotStep) * lotStep));

   return lotSize;
}

//+------------------------------------------------------------------+
//| Reset strategy to passive state                                 |
//+------------------------------------------------------------------+
void CFibReversalExpert::ResetStrategy(void)
{
   m_tradingPhase = PHASE_PASSIVE;
   m_fibHighPrice = 0;
   m_fibLowPrice = 0;
   m_fibLevel236 = 0;
   m_fibLevel500 = 0;
   m_firstFractalPrice = 0;
   m_secondFractalPrice = 0;
   m_firstFractalIsUp = false;
   m_secondFractalIsUp = false;
   m_firstFractalTime = 0;
   m_secondFractalTime = 0;
   m_longTicket = 0;
   m_shortTicket = 0;
   m_longEntryPrice = 0;
   m_shortEntryPrice = 0;
   m_waitingForNewBar = false;

   printf("Strategy reset to PASSIVE state");
}

//+------------------------------------------------------------------+
//| Log heartbeat information                                        |
//+------------------------------------------------------------------+
void CFibReversalExpert::LogHeartbeat(void)
{
   static datetime lastHeartbeat = 0;
   datetime currentTime = TimeCurrent();

   if(currentTime - lastHeartbeat >= 3600) // Log every hour
   {
      lastHeartbeat = currentTime;

      string phaseStr = "";
      switch(m_tradingPhase)
      {
         case PHASE_PASSIVE: phaseStr = "PASSIVE"; break;
         case PHASE_ACTIVE_LONG: phaseStr = "ACTIVE_LONG"; break;
         case PHASE_ACTIVE_SHORT: phaseStr = "ACTIVE_SHORT"; break;
      }

      printf("=== HEARTBEAT === Phase: %s | Fib Range: %.5f-%.5f | Levels: 236=%.5f, 500=%.5f | Positions: Long=%I64u, Short=%I64u",
             phaseStr, m_fibLowPrice, m_fibHighPrice, m_fibLevel236, m_fibLevel500, m_longTicket, m_shortTicket);
   }
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   CFibReversalExpert expert;
   if(!expert.Init())
   {
      printf("Expert initialization failed");
      return(INIT_FAILED);
   }

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   CFibReversalExpert expert;
   expert.Deinit();
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   static CFibReversalExpert expert;
   static bool initialized = false;

   if(!initialized)
   {
      if(!expert.Init())
         return;
      initialized = true;
   }

   expert.Processing();
   expert.LogHeartbeat();
}
