//+------------------------------------------------------------------+
//|                                        FibReversal_v4_synthesized.mq5 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "4.00"

#include <Expert\Expert.mqh>
#include <Expert\Signal\SignalMA.mqh>
#include <Expert\Trailing\TrailingNone.mqh>
#include <Expert\Money\MoneyFixedLot.mqh>
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>

// Input parameters - synthesized from both versions
input double InpPriceChangePercentage = 3.0;    // Minimum price change percentage (onurFibo.py: MIN_YUZDE_FARK = 0.03)
input double InpFibonacciLevel = 0.236;         // Fibonacci retracement level for entry
input double InpHardStopPercentage = 1.5;       // Hard stop percentage (onurFibo.py: HARD_STOP_YUZDE = 0.015)
input double InpRiskPercentage = 2.0;           // Risk percentage per trade
input int    InpMagicNumber = 123456;           // Magic number for trades

// Trading phases - from v1 but simplified like v3
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE = 0,        // Looking for patterns
   PHASE_CANDIDATE_LONG,     // Found candidate fibonacci (like onurFibo.py aday_fib)
   PHASE_CANDIDATE_SHORT,    // Found candidate fibonacci
   PHASE_ACTIVE_LONG,        // Active fibonacci long (like onurFibo.py aktif_fib)
   PHASE_ACTIVE_SHORT        // Active fibonacci short
};

// Global variables
class CFibReversalSynthesized : public CExpert
{
private:
   // Trading objects
   CTrade            m_trade;
   CSymbolInfo       m_symbol;
   CPositionInfo     m_position;
   
   // Strategy state variables
   ENUM_TRADING_PHASE m_tradingPhase;
   string            m_mode;                    // Python style: 'Yükseliş Fibi Arıyor' or 'Düşüş Fibi Arıyor'
   
   // Fibonacci levels and prices
   double            m_fibHighPrice;            // Peak price (tepe_fiyat)
   double            m_fibLowPrice;             // Trough price (dip_fiyat)
   datetime          m_fibHighTime;             // Peak time (tepe_zaman)
   datetime          m_fibLowTime;              // Trough time (dip_zaman)
   double            m_fibLevel236;             // 23.6% Fibonacci level
   double            m_fibLevel500;             // 50% Fibonacci level
   
   // Locked peak/trough tracking (Python style)
   double            m_lockedPeakPrice;         // kilitli_tepe_fiyat
   datetime          m_lockedPeakTime;          // kilitli_tepe_zaman
   double            m_lockedTroughPrice;       // kilitli_dip_fiyat
   datetime          m_lockedTroughTime;        // kilitli_dip_zaman
   
   // Position tracking
   ulong             m_longTicket;
   ulong             m_shortTicket;
   double            m_longEntryPrice;
   double            m_shortEntryPrice;
   
   // Bar processing
   datetime          m_lastProcessedBarTime;
   bool              m_waitingForNewBar;

public:
   CFibReversalSynthesized(void);
   ~CFibReversalSynthesized(void);
   
   virtual bool      Init(void);
   virtual void      Deinit(void);
   virtual bool      Processing(void);
   
protected:
   bool              InitCheckParameters(void);
   bool              ProcessStrategy(void);
   bool              DetectPatterns(void);
   bool              ProcessCandidateFib(void);
   bool              ProcessActiveFib(void);
   void              CalculateFibonacciLevels(void);
   bool              OpenLongPosition(void);
   bool              OpenShortPosition(void);
   bool              CloseLongPosition(void);
   bool              CloseShortPosition(void);
   double            CalculateLotSize(double stopLossDistance);
   void              ResetStrategy(void);
   void              LogState(void);
};

//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalSynthesized::CFibReversalSynthesized(void) :
   m_tradingPhase(PHASE_PASSIVE),
   m_mode("Yükseliş Fibi Arıyor"),
   m_fibHighPrice(0),
   m_fibLowPrice(0),
   m_fibHighTime(0),
   m_fibLowTime(0),
   m_fibLevel236(0),
   m_fibLevel500(0),
   m_lockedPeakPrice(0),
   m_lockedPeakTime(0),
   m_lockedTroughPrice(DBL_MAX),
   m_lockedTroughTime(0),
   m_longTicket(0),
   m_shortTicket(0),
   m_longEntryPrice(0),
   m_shortEntryPrice(0),
   m_lastProcessedBarTime(0),
   m_waitingForNewBar(false)
{
}

//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalSynthesized::~CFibReversalSynthesized(void)
{
}

//+------------------------------------------------------------------+
//| Initialization function                                          |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::Init(void)
{
   if(!InitCheckParameters())
      return false;
      
   if(!m_symbol.Name(Symbol()))
   {
      printf("Error initializing symbol");
      return false;
   }
   
   m_trade.SetExpertMagicNumber(InpMagicNumber);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());
   
   printf("FibReversal v4 Synthesized initialized successfully");
   printf("Parameters: PriceChange=%.1f%%, FibLevel=%.3f, HardStop=%.1f%%, Risk=%.1f%%",
          InpPriceChangePercentage, InpFibonacciLevel, InpHardStopPercentage, InpRiskPercentage);
   
   return true;
}

//+------------------------------------------------------------------+
//| Deinitialization function                                        |
//+------------------------------------------------------------------+
void CFibReversalSynthesized::Deinit(void)
{
   printf("FibReversal v4 Synthesized deinitialized");
}

//+------------------------------------------------------------------+
//| Main processing function                                         |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::Processing(void)
{
   // Check for new 4H bar (like Python script processes bar by bar)
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);
   if(currentBarTime == m_lastProcessedBarTime)
      return false; // Wait for new bar
      
   m_lastProcessedBarTime = currentBarTime;
   
   return ProcessStrategy();
}

//+------------------------------------------------------------------+
//| Parameter validation                                             |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::InitCheckParameters(void)
{
   if(InpPriceChangePercentage <= 0)
   {
      printf("Error: Price change percentage must be positive");
      return false;
   }
   
   if(InpFibonacciLevel <= 0 || InpFibonacciLevel >= 1)
   {
      printf("Error: Fibonacci level must be between 0 and 1");
      return false;
   }
   
   if(InpHardStopPercentage <= 0)
   {
      printf("Error: Hard stop percentage must be positive");
      return false;
   }
   
   if(InpRiskPercentage <= 0)
   {
      printf("Error: Risk percentage must be positive");
      return false;
   }
   
   return true;
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk                                |
//+------------------------------------------------------------------+
double CFibReversalSynthesized::CalculateLotSize(double stopLossDistance)
{
   double balance = AccountInfoDouble(ACCOUNT_BALANCE);
   double riskAmount = balance * InpRiskPercentage / 100.0;

   // Get symbol information
   double tickValue = m_symbol.TickValue();
   double tickSize = m_symbol.TickSize();
   double contractSize = m_symbol.ContractSize();

   // Calculate lot size based on risk amount and stop loss distance
   // Risk = Lot Size * Contract Size * Stop Loss Distance * Tick Value / Tick Size
   double lotSize = riskAmount / (contractSize * stopLossDistance * tickValue / tickSize);

   // Apply symbol constraints
   double minLot = m_symbol.LotsMin();
   double maxLot = m_symbol.LotsMax();
   double lotStep = m_symbol.LotsStep();

   // Round to valid lot step
   lotSize = MathRound(lotSize / lotStep) * lotStep;

   // Ensure within bounds
   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));

   // Additional safety check - ensure we have enough margin
   double marginRequired = m_symbol.MarginInitial() * lotSize;
   double freeMargin = AccountInfoDouble(ACCOUNT_MARGIN_FREE);

   if(marginRequired > freeMargin * 0.8) // Use only 80% of free margin for safety
   {
      lotSize = (freeMargin * 0.8) / m_symbol.MarginInitial();
      lotSize = MathRound(lotSize / lotStep) * lotStep;
      lotSize = MathMax(minLot, lotSize);
      printf("Lot size adjusted for margin: %.2f (required margin: %.2f, free margin: %.2f)",
             lotSize, marginRequired, freeMargin);
   }

   return lotSize;
}

//+------------------------------------------------------------------+
//| Reset strategy to passive phase                                 |
//+------------------------------------------------------------------+
void CFibReversalSynthesized::ResetStrategy(void)
{
   m_tradingPhase = PHASE_PASSIVE;
   m_fibHighPrice = 0;
   m_fibLowPrice = 0;
   m_fibHighTime = 0;
   m_fibLowTime = 0;
   m_fibLevel236 = 0;
   m_fibLevel500 = 0;
   m_lockedPeakPrice = 0;
   m_lockedPeakTime = 0;
   m_lockedTroughPrice = DBL_MAX;
   m_lockedTroughTime = 0;
}

//+------------------------------------------------------------------+
//| Log current strategy state                                       |
//+------------------------------------------------------------------+
void CFibReversalSynthesized::LogState(void)
{
   printf("State: Phase=%d, Mode=%s, Peak=%.5f, Trough=%.5f, Fib236=%.5f, Fib500=%.5f",
          m_tradingPhase, m_mode, m_fibHighPrice, m_fibLowPrice, m_fibLevel236, m_fibLevel500);
}

// Global expert instance
CFibReversalSynthesized ExtExpert;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   if(!ExtExpert.Init())
      return INIT_FAILED;
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ExtExpert.Deinit();
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   ExtExpert.Processing();
}

//+------------------------------------------------------------------+
//| Main strategy processing - synthesized from both versions       |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::ProcessStrategy(void)
{
   // Check existing positions first
   int positionsTotal = PositionsTotal();

   if(positionsTotal > 0)
   {
      // Handle position management
      if(m_longTicket > 0 && m_position.SelectByTicket(m_longTicket))
         return CloseLongPosition();
      if(m_shortTicket > 0 && m_position.SelectByTicket(m_shortTicket))
         return CloseShortPosition();
   }
   else
   {
      // No positions - process strategy phases
      switch(m_tradingPhase)
      {
         case PHASE_PASSIVE:
            return DetectPatterns();
         case PHASE_CANDIDATE_LONG:
         case PHASE_CANDIDATE_SHORT:
            return ProcessCandidateFib();
         case PHASE_ACTIVE_LONG:
         case PHASE_ACTIVE_SHORT:
            return ProcessActiveFib();
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Pattern detection - Python style close0 > close1 > close2      |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::DetectPatterns(void)
{
   // Need at least 3 bars for pattern detection
   if(iBars(NULL, PERIOD_H4) < 3)
      return false;

   // Get close prices for pattern detection (Python style)
   double close0 = iClose(NULL, PERIOD_H4, 2); // close0 (oldest)
   double close1 = iClose(NULL, PERIOD_H4, 1); // close1 (middle)
   double close2 = iClose(NULL, PERIOD_H4, 0); // close2 (newest)

   // Python pattern detection logic
   bool bullishPattern = (close0 > close1 && close1 > close2);
   bool bearishPattern = (close0 < close1 && close1 < close2);

   // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor'
   if(bullishPattern && m_mode != "Yükseliş Fibi Arıyor")
   {
      printf("BULLISH PATTERN: close0=%.5f > close1=%.5f > close2=%.5f - switching to bullish mode", close0, close1, close2);
      m_mode = "Yükseliş Fibi Arıyor";

      // Lock peak from last 3 bars (Python style)
      double high0 = iHigh(NULL, PERIOD_H4, 2);
      double high1 = iHigh(NULL, PERIOD_H4, 1);
      double high2 = iHigh(NULL, PERIOD_H4, 0);

      m_lockedPeakPrice = MathMax(high0, MathMax(high1, high2));
      m_lockedPeakTime = iTime(NULL, PERIOD_H4, 0); // Current time

      printf("Locked peak: %.5f at %s", m_lockedPeakPrice, TimeToString(m_lockedPeakTime));
      return true;
   }
   // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor'
   else if(bearishPattern && m_mode != "Düşüş Fibi Arıyor")
   {
      printf("BEARISH PATTERN: close0=%.5f < close1=%.5f < close2=%.5f - switching to bearish mode", close0, close1, close2);
      m_mode = "Düşüş Fibi Arıyor";

      // Lock trough from last 3 bars (Python style)
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);

      m_lockedTroughPrice = MathMin(low0, MathMin(low1, low2));
      m_lockedTroughTime = iTime(NULL, PERIOD_H4, 0); // Current time

      printf("Locked trough: %.5f at %s", m_lockedTroughPrice, TimeToString(m_lockedTroughTime));
      return true;
   }

   // Continue looking for fibonacci candidates based on current mode
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);
   datetime currentTime = iTime(NULL, PERIOD_H4, 0);

   if(m_mode == "Yükseliş Fibi Arıyor")
   {
      // Python: if row['High'] > kilitli_tepe_fiyat: kilitli_tepe_fiyat = row['High']
      if(currentHigh > m_lockedPeakPrice)
      {
         m_lockedPeakPrice = currentHigh;
         m_lockedPeakTime = currentTime;
         printf("Peak updated to %.5f", m_lockedPeakPrice);
      }
      // Python: elif kilitli_tepe_zaman: check for fibonacci candidate
      else if(m_lockedPeakTime > 0)
      {
         double priceChange = (m_lockedPeakPrice - currentLow) / currentLow;
         // Python script uses exactly 3% (MIN_YUZDE_FARK = 0.03)
         if(priceChange >= InpPriceChangePercentage / 100.0)
         {
            // Create candidate fibonacci (Python: aday_fib)
            m_fibHighPrice = m_lockedPeakPrice;
            m_fibHighTime = m_lockedPeakTime;
            m_fibLowPrice = currentLow;
            m_fibLowTime = currentTime;
            m_tradingPhase = PHASE_CANDIDATE_LONG;

            CalculateFibonacciLevels();
            printf("BULLISH CANDIDATE FIB: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange * 100);
            return true;
         }
      }
   }
   else if(m_mode == "Düşüş Fibi Arıyor")
   {
      // Python: if row['Low'] < kilitli_dip_fiyat: kilitli_dip_fiyat = row['Low']
      if(currentLow < m_lockedTroughPrice)
      {
         m_lockedTroughPrice = currentLow;
         m_lockedTroughTime = currentTime;
         printf("Trough updated to %.5f", m_lockedTroughPrice);
      }
      // Python: elif kilitli_dip_zaman: check for fibonacci candidate
      else if(m_lockedTroughTime > 0)
      {
         double priceChange = (currentHigh - m_lockedTroughPrice) / m_lockedTroughPrice;
         // Python script uses exactly 3% (MIN_YUZDE_FARK = 0.03)
         if(priceChange >= InpPriceChangePercentage / 100.0)
         {
            // Create candidate fibonacci (Python: aday_fib)
            m_fibHighPrice = currentHigh;
            m_fibHighTime = currentTime;
            m_fibLowPrice = m_lockedTroughPrice;
            m_fibLowTime = m_lockedTroughTime;
            m_tradingPhase = PHASE_CANDIDATE_SHORT;

            CalculateFibonacciLevels();
            printf("BEARISH CANDIDATE FIB: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange * 100);
            return true;
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process candidate fibonacci - More tolerant Python logic        |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::ProcessCandidateFib(void)
{
   double currentClose = iClose(NULL, PERIOD_H4, 0);
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   if(m_tradingPhase == PHASE_CANDIDATE_LONG)
   {
      // More tolerant invalidation - allow some extension beyond original range
      // Only invalidate if price moves significantly beyond range (like Python script behavior)
      double tolerance = (m_fibHighPrice - m_fibLowPrice) * 0.1; // 10% tolerance

      if(currentClose < (m_fibLowPrice - tolerance))
      {
         printf("BULLISH CANDIDATE INVALIDATED: Close %.5f significantly below trough %.5f (tolerance: %.5f)",
                currentClose, m_fibLowPrice, tolerance);
         ResetStrategy();
         return false;
      }

      // Update trough if current low is lower (Python script behavior)
      if(currentLow < m_fibLowPrice)
      {
         m_fibLowPrice = currentLow;
         CalculateFibonacciLevels(); // Recalculate levels with new trough
         printf("Bullish candidate trough updated to %.5f, new Fib236=%.5f", m_fibLowPrice, m_fibLevel236);
      }

      // Python: elif row['Close'] > aday_fib.level_236: aktif_fib = aday_fib
      if(currentClose > m_fibLevel236)
      {
         printf("BULLISH CANDIDATE ACTIVATED: Close %.5f > Fib236 %.5f", currentClose, m_fibLevel236);
         m_tradingPhase = PHASE_ACTIVE_LONG;
         return true;
      }
   }
   else if(m_tradingPhase == PHASE_CANDIDATE_SHORT)
   {
      // More tolerant invalidation - allow some extension beyond original range
      double tolerance = (m_fibHighPrice - m_fibLowPrice) * 0.1; // 10% tolerance

      if(currentClose > (m_fibHighPrice + tolerance))
      {
         printf("BEARISH CANDIDATE INVALIDATED: Close %.5f significantly above peak %.5f (tolerance: %.5f)",
                currentClose, m_fibHighPrice, tolerance);
         ResetStrategy();
         return false;
      }

      // Update peak if current high is higher (Python script behavior)
      if(currentHigh > m_fibHighPrice)
      {
         m_fibHighPrice = currentHigh;
         CalculateFibonacciLevels(); // Recalculate levels with new peak
         printf("Bearish candidate peak updated to %.5f, new Fib236=%.5f", m_fibHighPrice, m_fibLevel236);
      }

      // Python: elif row['Close'] < aday_fib.level_236: aktif_fib = aday_fib
      if(currentClose < m_fibLevel236)
      {
         printf("BEARISH CANDIDATE ACTIVATED: Close %.5f < Fib236 %.5f", currentClose, m_fibLevel236);
         m_tradingPhase = PHASE_ACTIVE_SHORT;
         return true;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Process active fibonacci - Python aktif_fib logic               |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::ProcessActiveFib(void)
{
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
      return OpenLongPosition();
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
      return OpenShortPosition();

   return false;
}

//+------------------------------------------------------------------+
//| Calculate Fibonacci levels - exact Python logic                 |
//+------------------------------------------------------------------+
void CFibReversalSynthesized::CalculateFibonacciLevels(void)
{
   double diff = MathAbs(m_fibHighPrice - m_fibLowPrice);

   if(m_tradingPhase == PHASE_CANDIDATE_LONG || m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python: self.level_236 = self.dip_fiyat + 0.236 * diff
      // Python: self.level_500 = self.dip_fiyat + 0.5 * diff
      m_fibLevel236 = m_fibLowPrice + (InpFibonacciLevel * diff);
      m_fibLevel500 = m_fibLowPrice + (0.5 * diff);
   }
   else
   {
      // Python: self.level_236 = self.tepe_fiyat - 0.236 * diff
      // Python: self.level_500 = self.tepe_fiyat - 0.5 * diff
      m_fibLevel236 = m_fibHighPrice - (InpFibonacciLevel * diff);
      m_fibLevel500 = m_fibHighPrice - (0.5 * diff);
   }

   printf("Fibonacci levels calculated: 236=%.5f, 500=%.5f", m_fibLevel236, m_fibLevel500);
}

//+------------------------------------------------------------------+
//| Open long position - synthesized entry logic                    |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::OpenLongPosition(void)
{
   // Refresh symbol quotes
   if(!m_symbol.RefreshRates())
   {
      printf("Error refreshing symbol rates");
      return false;
   }

   double ask = m_symbol.Ask();
   if(ask <= 0)
   {
      printf("Invalid Ask price: %.5f", ask);
      return false;
   }

   // Calculate lot size based on hard stop distance
   double hardStopDistance = ask * InpHardStopPercentage / 100.0;
   double lotSize = CalculateLotSize(hardStopDistance);

   if(lotSize <= 0)
   {
      printf("Invalid lot size calculated: %.2f", lotSize);
      return false;
   }

   m_longEntryPrice = ask;

   printf("Opening long position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
          ask, m_fibLevel236, m_fibLevel500, lotSize);

   if(m_trade.Buy(lotSize, Symbol(), ask))
   {
      m_longTicket = m_trade.ResultOrder();
      printf("Long position opened successfully, ticket: %I64u", m_longTicket);
      return true;
   }
   else
   {
      printf("Error opening long position: %s", m_trade.ResultComment());
      return false;
   }
}

//+------------------------------------------------------------------+
//| Open short position - synthesized entry logic                   |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::OpenShortPosition(void)
{
   // Refresh symbol quotes
   if(!m_symbol.RefreshRates())
   {
      printf("Error refreshing symbol rates");
      return false;
   }

   double bid = m_symbol.Bid();
   if(bid <= 0)
   {
      printf("Invalid Bid price: %.5f", bid);
      return false;
   }

   // Calculate lot size based on hard stop distance
   double hardStopDistance = bid * InpHardStopPercentage / 100.0;
   double lotSize = CalculateLotSize(hardStopDistance);

   if(lotSize <= 0)
   {
      printf("Invalid lot size calculated: %.2f", lotSize);
      return false;
   }

   m_shortEntryPrice = bid;

   printf("Opening short position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
          bid, m_fibLevel236, m_fibLevel500, lotSize);

   if(m_trade.Sell(lotSize, Symbol(), bid))
   {
      m_shortTicket = m_trade.ResultOrder();
      printf("Short position opened successfully, ticket: %I64u", m_shortTicket);
      return true;
   }
   else
   {
      printf("Error opening short position: %s", m_trade.ResultComment());
      return false;
   }
}

//+------------------------------------------------------------------+
//| Close long position - Python exit conditions                    |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::CloseLongPosition(void)
{
   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   bool success = false;
   bool fail = false;
   double profitPercent = 0;

   // Python exit conditions for long positions:
   // 1. Take profit: row['High'] >= aktif_fib.level_500
   if(currentHigh >= m_fibLevel500)
   {
      success = true;
      profitPercent = (m_fibLevel500 - m_fibLevel236) / m_fibLevel236 * 100.0;
      printf("LONG TP HIT: High %.5f >= Fib500 %.5f, Profit: %.2f%%", currentHigh, m_fibLevel500, profitPercent);
   }
   // 2. Hard stop: row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE)
   else
   {
      double hardStopLevel = m_fibLevel236 * (1.0 - InpHardStopPercentage / 100.0);
      if(currentLow < hardStopLevel)
      {
         fail = true;
         profitPercent = -InpHardStopPercentage;
         printf("LONG HARD STOP HIT: Low %.5f < Stop %.5f, Loss: %.2f%%", currentLow, hardStopLevel, profitPercent);
      }
   }

   if(success || fail)
   {
      if(m_trade.PositionClose(Symbol()))
      {
         printf("Long position closed successfully - %s", success ? "TP" : "STOP");
         m_longTicket = 0;
         m_longEntryPrice = 0;
         ResetStrategy();
         return true;
      }
      else
      {
         printf("Error closing long position: %s", m_trade.ResultComment());
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Close short position - Python exit conditions                   |
//+------------------------------------------------------------------+
bool CFibReversalSynthesized::CloseShortPosition(void)
{
   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   bool success = false;
   bool fail = false;
   double profitPercent = 0;

   // Python exit conditions for short positions:
   // 1. Take profit: row['Low'] <= aktif_fib.level_500
   if(currentLow <= m_fibLevel500)
   {
      success = true;
      profitPercent = (m_fibLevel236 - m_fibLevel500) / m_fibLevel236 * 100.0;
      printf("SHORT TP HIT: Low %.5f <= Fib500 %.5f, Profit: %.2f%%", currentLow, m_fibLevel500, profitPercent);
   }
   // 2. Hard stop: row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE)
   else
   {
      double hardStopLevel = m_fibLevel236 * (1.0 + InpHardStopPercentage / 100.0);
      if(currentHigh > hardStopLevel)
      {
         fail = true;
         profitPercent = -InpHardStopPercentage;
         printf("SHORT HARD STOP HIT: High %.5f > Stop %.5f, Loss: %.2f%%", currentHigh, hardStopLevel, profitPercent);
      }
   }

   if(success || fail)
   {
      if(m_trade.PositionClose(Symbol()))
      {
         printf("Short position closed successfully - %s", success ? "TP" : "STOP");
         m_shortTicket = 0;
         m_shortEntryPrice = 0;
         ResetStrategy();
         return true;
      }
      else
      {
         printf("Error closing short position: %s", m_trade.ResultComment());
      }
   }

   return false;
}
