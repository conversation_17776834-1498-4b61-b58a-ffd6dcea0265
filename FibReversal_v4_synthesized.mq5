//+------------------------------------------------------------------+
//| FibReversal_v4_synthesized.mq5                                  |
//| Synthesized EA combining profitability of v3 with logic of v1   |
//| Based on onurFibo.py - 285 trades target                        |
//+------------------------------------------------------------------+
#property copyright "Synthesized from v1 and v3"
#property version   "4.00"
#property strict

#include <Expert\Expert.mqh>
#include <Expert\Signal\SignalMA.mqh>
#include <Expert\Trailing\TrailingNone.mqh>
#include <Expert\Money\MoneyFixedLot.mqh>

//--- Input parameters (matching Python script)
input double InpPriceChangePercentage = 3.0;    // MIN_YUZDE_FARK = 0.03 (3%)
input double InpFibonacciLevel = 0.236;         // Fibonacci 0.236 level
input double InpHardStopPercentage = 1.5;       // HARD_STOP_YUZDE = 0.015 (1.5%)
input double InpLotSize = 0.1;                  // Fixed lot size
input int InpMagicNumber = 12345;               // Magic number for trades

//--- Trading phases (matching Python modes)
enum ENUM_TRADING_PHASE
{
   PHASE_PASSIVE = 0,           // Not looking for any pattern
   PHASE_ACTIVE_LONG = 1,       // Looking for bullish fibonacci (Yükseliş Fibi Arıyor)
   PHASE_ACTIVE_SHORT = 2       // Looking for bearish fibonacci (Düşüş Fibi Arıyor)
};

//--- Global variables
ENUM_TRADING_PHASE m_tradingPhase = PHASE_PASSIVE;
double m_fibHighPrice = 0;
double m_fibLowPrice = 0;
double m_fibLevel236 = 0;
double m_fibLevel500 = 0;
int m_longTicket = 0;
int m_shortTicket = 0;
datetime m_fibHighTime = 0;
datetime m_fibLowTime = 0;

// v1 style validation variables
bool m_candle4hClosed = false;
bool m_waitingForTouch = false;
double m_longEntryPrice = 0;
double m_shortEntryPrice = 0;

// v3 style tracking variables (Python matching)
double m_kilitli_tepe_fiyat = 0;
datetime m_kilitli_tepe_zaman = 0;
double m_kilitli_dip_fiyat = DBL_MAX;
datetime m_kilitli_dip_zaman = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
   Print("FibReversal_v4_synthesized EA initialized - Target: 285 trades like Python script");
   Print("Parameters: PriceChange=", InpPriceChangePercentage, "%, FibLevel=", InpFibonacciLevel, 
         ", HardStop=", InpHardStopPercentage, "%");
   
   // Reset all variables
   ResetStrategy();
   
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   Print("FibReversal_v4_synthesized EA deinitialized");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
   // Process existing positions first (v3 style - direct Python matching)
   if(m_longTicket != 0)
   {
      if(!LongClosed())
         return; // Continue monitoring position
   }
   
   if(m_shortTicket != 0)
   {
      if(!ShortClosed())
         return; // Continue monitoring position
   }
   
   // Detect fractals and update trading phase (v3 Python style)
   DetectFractals();
   
   // Process active phase (hybrid approach)
   ProcessActivePhase();
}

//+------------------------------------------------------------------+
//| Detect fractals - v3 Python style pattern detection            |
//+------------------------------------------------------------------+
bool DetectFractals()
{
   // Get last 3 closes for pattern detection (Python style)
   double close0 = iClose(NULL, PERIOD_H4, 2); // 3 bars ago
   double close1 = iClose(NULL, PERIOD_H4, 1); // 2 bars ago  
   double close2 = iClose(NULL, PERIOD_H4, 0); // 1 bar ago (current completed)
   
   // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor'
   bool bullishPattern = (close0 > close1 && close1 > close2);
   
   // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor'
   bool bearishPattern = (close0 < close1 && close1 < close2);
   
   // v3 style: Only detect new patterns when NOT already in fibonacci tracking mode
   if(bullishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      Print("BULLISH PATTERN: close0 > close1 > close2 - switching to bullish fib mode");
      m_tradingPhase = PHASE_ACTIVE_LONG;
      
      // Python: kilitli_tepe_fiyat = df['High'].iloc[i-2:i+1].max()
      double high0 = iHigh(NULL, PERIOD_H4, 2);
      double high1 = iHigh(NULL, PERIOD_H4, 1);
      double high2 = iHigh(NULL, PERIOD_H4, 0);
      
      m_kilitli_tepe_fiyat = MathMax(high0, MathMax(high1, high2));
      m_kilitli_tepe_zaman = TimeCurrent();
      
      Print("Locked peak: ", m_kilitli_tepe_fiyat, " at ", TimeToString(m_kilitli_tepe_zaman));
      return true;
   }
   
   if(bearishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      Print("BEARISH PATTERN: close0 < close1 < close2 - switching to bearish fib mode");
      m_tradingPhase = PHASE_ACTIVE_SHORT;
      
      // Python: kilitli_dip_fiyat = df['Low'].iloc[i-2:i+1].min()
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);
      
      m_kilitli_dip_fiyat = MathMin(low0, MathMin(low1, low2));
      m_kilitli_dip_zaman = TimeCurrent();
      
      Print("Locked trough: ", m_kilitli_dip_fiyat, " at ", TimeToString(m_kilitli_dip_zaman));
      return true;
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Process active phase - Hybrid v3 + v1 approach                |
//+------------------------------------------------------------------+
bool ProcessActivePhase()
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;
   
   // Update peak/trough tracking (v3 Python style)
   UpdatePeakTroughTracking();
   
   // Try to open positions (hybrid approach)
   if(m_tradingPhase == PHASE_ACTIVE_LONG && m_fibLevel236 > 0)
   {
      return LongOpened();
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT && m_fibLevel236 > 0)
   {
      return ShortOpened();
   }
   
   return false;
}

//+------------------------------------------------------------------+
//| Update peak/trough tracking - v3 Python style                 |
//+------------------------------------------------------------------+
void UpdatePeakTroughTracking()
{
   if(m_tradingPhase == PHASE_PASSIVE)
      return;
   
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);
   datetime currentTime = TimeCurrent();
   
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python: if row['High'] > kilitli_tepe_fiyat: kilitli_tepe_fiyat = row['High']
      if(currentHigh > m_kilitli_tepe_fiyat)
      {
         m_kilitli_tepe_fiyat = currentHigh;
         m_kilitli_tepe_zaman = currentTime;
         Print("Peak updated to ", m_kilitli_tepe_fiyat, " (bullish mode)");
      }
      
      // Python: fark = (kilitli_tepe_fiyat - row['Low']) / row['Low']
      double fark = (m_kilitli_tepe_fiyat - currentLow) / currentLow * 100.0;
      if(fark >= InpPriceChangePercentage)
      {
         if(m_fibHighPrice != m_kilitli_tepe_fiyat || m_fibLowPrice != currentLow)
         {
            m_fibHighPrice = m_kilitli_tepe_fiyat;
            m_fibLowPrice = currentLow;
            m_fibHighTime = m_kilitli_tepe_zaman;
            m_fibLowTime = currentTime;
            
            Print("Bullish fib candidate: Peak=", m_fibHighPrice, ", Trough=", m_fibLowPrice, 
                  ", Change=", fark, "%");
            CalculateFibonacciLevels();
         }
      }
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // Python: if row['Low'] < kilitli_dip_fiyat: kilitli_dip_fiyat = row['Low']
      if(currentLow < m_kilitli_dip_fiyat)
      {
         m_kilitli_dip_fiyat = currentLow;
         m_kilitli_dip_zaman = currentTime;
         Print("Trough updated to ", m_kilitli_dip_fiyat, " (bearish mode)");
      }
      
      // Python: fark = (row['High'] - kilitli_dip_fiyat) / kilitli_dip_fiyat
      double fark = (currentHigh - m_kilitli_dip_fiyat) / m_kilitli_dip_fiyat * 100.0;
      if(fark >= InpPriceChangePercentage)
      {
         if(m_fibHighPrice != currentHigh || m_fibLowPrice != m_kilitli_dip_fiyat)
         {
            m_fibHighPrice = currentHigh;
            m_fibLowPrice = m_kilitli_dip_fiyat;
            m_fibHighTime = currentTime;
            m_fibLowTime = m_kilitli_dip_zaman;
            
            Print("Bearish fib candidate: Peak=", m_fibHighPrice, ", Trough=", m_fibLowPrice, 
                  ", Change=", fark, "%");
            CalculateFibonacciLevels();
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Calculate Fibonacci levels                                       |
//+------------------------------------------------------------------+
void CalculateFibonacciLevels()
{
   if(m_fibHighPrice == 0 || m_fibLowPrice == 0)
      return;
   
   double fibRange = m_fibHighPrice - m_fibLowPrice;
   
   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // Python: self.level_236 = self.dip_fiyat + 0.236 * diff
      // Python: self.level_500 = self.dip_fiyat + 0.5 * diff
      m_fibLevel236 = m_fibLowPrice + (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibLowPrice + (fibRange * 0.5);
      
      Print("LONG Fibonacci levels: 236=", m_fibLevel236, ", 500=", m_fibLevel500);
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // Python: self.level_236 = self.tepe_fiyat - 0.236 * diff
      // Python: self.level_500 = self.tepe_fiyat - 0.5 * diff
      m_fibLevel236 = m_fibHighPrice - (fibRange * InpFibonacciLevel);
      m_fibLevel500 = m_fibHighPrice - (fibRange * 0.5);
      
      Print("SHORT Fibonacci levels: 236=", m_fibLevel236, ", 500=", m_fibLevel500);
   }
}

//+------------------------------------------------------------------+
//| Reset strategy to passive state                                 |
//+------------------------------------------------------------------+
void ResetStrategy()
{
   m_tradingPhase = PHASE_PASSIVE;
   m_fibHighPrice = 0;
   m_fibLowPrice = 0;
   m_fibLevel236 = 0;
   m_fibLevel500 = 0;
   m_fibHighTime = 0;
   m_fibLowTime = 0;
   
   // Reset v1 style variables
   m_candle4hClosed = false;
   m_waitingForTouch = false;
   m_longEntryPrice = 0;
   m_shortEntryPrice = 0;
   
   // Reset v3 style variables
   m_kilitli_tepe_fiyat = 0;
   m_kilitli_tepe_zaman = 0;
   m_kilitli_dip_fiyat = DBL_MAX;
   m_kilitli_dip_zaman = 0;
}

//+------------------------------------------------------------------+
//| Open long position - Hybrid v3 + v1 approach                  |
//+------------------------------------------------------------------+
bool LongOpened()
{
   if(m_longTicket != 0 || m_tradingPhase != PHASE_ACTIVE_LONG || m_fibLevel236 == 0)
      return false;

   // v3 style: Check if close price crosses above 236 level (immediate activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h >= m_fibLevel236)
   {
      // v1 style validation: Ensure 4H candle actually closed above level
      if(!m_candle4hClosed)
      {
         m_candle4hClosed = true;
         m_waitingForTouch = true;
         Print("4H candle closed above Fib 236 level: ", close4h, " >= ", m_fibLevel236);
         return false; // Wait for pullback touch
      }

      // v1 style: Wait for price to touch 236 level from above (pullback)
      if(m_waitingForTouch)
      {
         double currentLow = iLow(NULL, PERIOD_H4, 0);
         if(currentLow <= m_fibLevel236 * 1.001) // Small tolerance for touch
         {
            Print("Price touched Fib 236 from above, opening long position");

            double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            double hardStopDistance = ask * InpHardStopPercentage / 100.0;

            m_longTicket = OrderSend(_Symbol, OP_BUY, InpLotSize, ask, 3,
                                   ask - hardStopDistance, 0,
                                   "FibRev_Long", InpMagicNumber, 0, clrGreen);

            if(m_longTicket > 0)
            {
               m_longEntryPrice = ask;
               Print("Long position opened: Ticket=", m_longTicket, ", Entry=", ask,
                     ", Fib236=", m_fibLevel236, ", Fib500=", m_fibLevel500);
               return true;
            }
            else
            {
               Print("Failed to open long position. Error: ", GetLastError());
               return false;
            }
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Open short position - Hybrid v3 + v1 approach                 |
//+------------------------------------------------------------------+
bool ShortOpened()
{
   if(m_shortTicket != 0 || m_tradingPhase != PHASE_ACTIVE_SHORT || m_fibLevel236 == 0)
      return false;

   // v3 style: Check if close price crosses below 236 level (immediate activation)
   double close4h = iClose(NULL, PERIOD_H4, 0);

   if(close4h <= m_fibLevel236)
   {
      // v1 style validation: Ensure 4H candle actually closed below level
      if(!m_candle4hClosed)
      {
         m_candle4hClosed = true;
         m_waitingForTouch = true;
         Print("4H candle closed below Fib 236 level: ", close4h, " <= ", m_fibLevel236);
         return false; // Wait for pullback touch
      }

      // v1 style: Wait for price to touch 236 level from below (pullback)
      if(m_waitingForTouch)
      {
         double currentHigh = iHigh(NULL, PERIOD_H4, 0);
         if(currentHigh >= m_fibLevel236 * 0.999) // Small tolerance for touch
         {
            Print("Price touched Fib 236 from below, opening short position");

            double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            double hardStopDistance = bid * InpHardStopPercentage / 100.0;

            m_shortTicket = OrderSend(_Symbol, OP_SELL, InpLotSize, bid, 3,
                                    bid + hardStopDistance, 0,
                                    "FibRev_Short", InpMagicNumber, 0, clrRed);

            if(m_shortTicket > 0)
            {
               m_shortEntryPrice = bid;
               Print("Short position opened: Ticket=", m_shortTicket, ", Entry=", bid,
                     ", Fib236=", m_fibLevel236, ", Fib500=", m_fibLevel500);
               return true;
            }
            else
            {
               Print("Failed to open short position. Error: ", GetLastError());
               return false;
            }
         }
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Close long position - v3 Python style direct matching         |
//+------------------------------------------------------------------+
bool LongClosed()
{
   if(m_longTicket == 0)
      return true;

   // Check if position still exists
   if(!OrderSelect(m_longTicket, SELECT_BY_TICKET))
   {
      Print("Long position ", m_longTicket, " no longer exists");
      m_longTicket = 0;
      ResetStrategy();
      return true;
   }

   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   // Python exit conditions:
   // 1. Take profit: row['High'] >= aktif_fib.level_500
   bool tpHit = (currentHigh >= m_fibLevel500);

   // 2. Hard stop: row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 - InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentLow < hardStopLevel);

   if(tpHit || hardStopHit)
   {
      double closePrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
      bool closed = OrderClose(m_longTicket, InpLotSize, closePrice, 3, clrBlue);

      if(closed)
      {
         string exitReason = tpHit ? "Take Profit (Fib 500)" : "Hard Stop";
         double profitPct = tpHit ?
            (m_fibLevel500 - m_fibLevel236) / m_fibLevel236 * 100.0 :
            -InpHardStopPercentage;

         Print("Long position closed: ", exitReason, ", Profit: ", profitPct, "%");

         m_longTicket = 0;
         ResetStrategy();
         return true;
      }
      else
      {
         Print("Failed to close long position. Error: ", GetLastError());
         return false;
      }
   }

   return false;
}

//+------------------------------------------------------------------+
//| Close short position - v3 Python style direct matching        |
//+------------------------------------------------------------------+
bool ShortClosed()
{
   if(m_shortTicket == 0)
      return true;

   // Check if position still exists
   if(!OrderSelect(m_shortTicket, SELECT_BY_TICKET))
   {
      Print("Short position ", m_shortTicket, " no longer exists");
      m_shortTicket = 0;
      ResetStrategy();
      return true;
   }

   // Get current bar data (Python style)
   double currentHigh = iHigh(NULL, PERIOD_H4, 0);
   double currentLow = iLow(NULL, PERIOD_H4, 0);

   // Python exit conditions:
   // 1. Take profit: row['Low'] <= aktif_fib.level_500
   bool tpHit = (currentLow <= m_fibLevel500);

   // 2. Hard stop: row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE)
   double hardStopLevel = m_fibLevel236 * (1.0 + InpHardStopPercentage / 100.0);
   bool hardStopHit = (currentHigh > hardStopLevel);

   if(tpHit || hardStopHit)
   {
      double closePrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
      bool closed = OrderClose(m_shortTicket, InpLotSize, closePrice, 3, clrBlue);

      if(closed)
      {
         string exitReason = tpHit ? "Take Profit (Fib 500)" : "Hard Stop";
         double profitPct = tpHit ?
            (m_fibLevel236 - m_fibLevel500) / m_fibLevel236 * 100.0 :
            -InpHardStopPercentage;

         Print("Short position closed: ", exitReason, ", Profit: ", profitPct, "%");

         m_shortTicket = 0;
         ResetStrategy();
         return true;
      }
      else
      {
         Print("Failed to close short position. Error: ", GetLastError());
         return false;
      }
   }

   return false;
}
