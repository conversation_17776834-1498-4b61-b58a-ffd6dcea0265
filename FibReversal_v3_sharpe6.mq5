//+------------------------------------------------------------------+
//|                                           Fibonacci Reversal EA |
//|                          Fibonacci-based reversal trading strategy |
//|                                  Rewritten to match onurFibo.py |
//+------------------------------------------------------------------+
#property version     "2.0"
#property description "Fibonacci Reversal Strategy - Exact Python Script Implementation"

#define FibReversalMagic 9765428

#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

// Strategy parameters (matching Python constants)
input double InpMinYuzdeFark = 3.0;                 // MIN_YUZDE_FARK (minimum percentage difference)
input double InpHardStopYuzde = 1.5;                // HARD_STOP_YUZDE (hard stop percentage)
input double InpRiskPercent = 2.0;                  // Risk percentage per trade
input double InpKaldirac = 15.0;                    // KALDIRAC (leverage multiplier for profit calculation)

// Trading modes (matching Python logic)
enum ENUM_TRADING_MODE
{
   MODE_YUKSELIS_FIBI_ARIYOR,    // 'Yükseliş Fibi Arıyor' - Looking for bullish fib
   MODE_DUSUS_FIBI_ARIYOR        // 'Düşüş Fibi Arıyor' - Looking for bearish fib
};

//+------------------------------------------------------------------+
//| Fibonacci Retracement class (matching Python FibonacciRetracement) |
//+------------------------------------------------------------------+
class CFibonacciRetracement
{
public:
   string            type;                         // 'Yükseliş' or 'Düşüş'
   double            tepe_fiyat;                   // Peak price
   datetime          tepe_zaman;                   // Peak time
   double            dip_fiyat;                    // Trough price
   datetime          dip_zaman;                    // Trough time
   double            level_236;                    // 0.236 Fibonacci level
   double            level_500;                    // 0.5 Fibonacci level

   CFibonacciRetracement(double peak_price, datetime peak_time, double trough_price, datetime trough_time, string fib_type)
   {
      type = fib_type;
      tepe_fiyat = peak_price;
      tepe_zaman = peak_time;
      dip_fiyat = trough_price;
      dip_zaman = trough_time;

      double diff = MathAbs(tepe_fiyat - dip_fiyat);
      if(type == "Yükseliş")
      {
         level_236 = dip_fiyat + 0.236 * diff;
         level_500 = dip_fiyat + 0.5 * diff;
      }
      else
      {
         level_236 = tepe_fiyat - 0.236 * diff;
         level_500 = tepe_fiyat - 0.5 * diff;
      }
   }
};

//+------------------------------------------------------------------+
//| Fibonacci Reversal Expert Advisor class (Python style)         |
//+------------------------------------------------------------------+
class CFibReversalExpert
{
protected:
   // Trading objects
   CSymbolInfo       m_symbol;                     // Symbol info object
   CTrade            m_trade;                      // Trading object
   CPositionInfo     m_position;                   // Position info object
   CAccountInfo      m_account;                    // Account info wrapper

   // Python script variables (exact match)
   ENUM_TRADING_MODE m_mode;                       // Current trading mode
   CFibonacciRetracement* m_aktif_fib;            // Active fibonacci (aktif_fib)
   CFibonacciRetracement* m_aday_fib;             // Candidate fibonacci (aday_fib)
   double            m_kilitli_tepe_fiyat;         // Locked peak price
   datetime          m_kilitli_tepe_zaman;         // Locked peak time
   double            m_kilitli_dip_fiyat;          // Locked trough price
   datetime          m_kilitli_dip_zaman;          // Locked trough time
   double            m_gecici_tepe_kilit;          // Temporary peak lock
   double            m_gecici_dip_kilit;           // Temporary trough lock
   double            m_sermaye;                    // Current capital (for logging)

   // Position tracking
   ulong             m_currentTicket;              // Current position ticket
   datetime          m_lastProcessedBarTime;       // Last processed bar time

public:
                     CFibReversalExpert(void);
                    ~CFibReversalExpert(void);
   bool              Init(void);
   bool              Processing(void);

protected:
   bool              InitCheckParameters(void);
   void              ProcessNewBar(void);
   void              CheckPatternDetection(int bar_index);
   void              UpdatePeakTroughTracking(int bar_index);
   void              CheckFibonacciActivation(int bar_index);
   void              CheckActivePositionExit(int bar_index);
   double            CalculateLotSize(void);
   void              OpenLongPosition(void);
   void              OpenShortPosition(void);
   void              ClosePosition(string reason, double kar_orani);
   void              ResetFibonacci(void);
};
//--- global expert
CFibReversalExpert ExtExpert;
//+------------------------------------------------------------------+
//| Constructor                                                      |
//+------------------------------------------------------------------+
CFibReversalExpert::CFibReversalExpert(void) : m_mode(MODE_YUKSELIS_FIBI_ARIYOR),
                                               m_aktif_fib(NULL),
                                               m_aday_fib(NULL),
                                               m_kilitli_tepe_fiyat(0),
                                               m_kilitli_tepe_zaman(0),
                                               m_kilitli_dip_fiyat(DBL_MAX),
                                               m_kilitli_dip_zaman(0),
                                               m_gecici_tepe_kilit(0),
                                               m_gecici_dip_kilit(0),
                                               m_sermaye(100.0),
                                               m_currentTicket(0),
                                               m_lastProcessedBarTime(0)
{
   // Initialize like Python script
}
//+------------------------------------------------------------------+
//| Destructor                                                       |
//+------------------------------------------------------------------+
CFibReversalExpert::~CFibReversalExpert(void)
{
   // Clean up fibonacci objects
   if(m_aktif_fib != NULL)
   {
      delete m_aktif_fib;
      m_aktif_fib = NULL;
   }
   if(m_aday_fib != NULL)
   {
      delete m_aday_fib;
      m_aday_fib = NULL;
   }
}
//+------------------------------------------------------------------+
//| Initialization and checking for input parameters                 |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Init(void)
{
   // Initialize trading objects
   m_symbol.Name(Symbol());
   m_trade.SetExpertMagicNumber(FibReversalMagic);
   m_trade.SetMarginMode();
   m_trade.SetTypeFillingBySymbol(Symbol());

   // Set trading deviation
   m_trade.SetDeviationInPoints(30);

   // Check parameters
   if(!InitCheckParameters())
      return false;

   // Log initialization
   printf("=== Fibonacci Reversal EA v4 Initialized ===");
   printf("Parameters: MinYuzdeFark=%.1f%%, HardStop=%.1f%%, Risk=%.1f%%",
          InpMinYuzdeFark, InpHardStopYuzde, InpRiskPercent);

   return true;
}
//+------------------------------------------------------------------+
//| Checking for input parameters                                    |
//+------------------------------------------------------------------+
bool CFibReversalExpert::InitCheckParameters(void)
{
   // Validate minimum percentage difference
   if(InpMinYuzdeFark <= 0)
   {
      printf("MinYuzdeFark must be greater than 0, current value: %f", InpMinYuzdeFark);
      return false;
   }

   // Validate hard stop percentage
   if(InpHardStopYuzde <= 0)
   {
      printf("HardStopYuzde must be greater than 0, current value: %f", InpHardStopYuzde);
      return false;
   }

   // Validate risk percentage
   if(InpRiskPercent <= 0 || InpRiskPercent > 100)
   {
      printf("Risk percentage must be between 0 and 100, current value: %f", InpRiskPercent);
      return false;
   }

   // Validate leverage
   if(InpKaldirac <= 0)
   {
      printf("Kaldirac must be greater than 0, current value: %f", InpKaldirac);
      return false;
   }

   return true;
}
//+------------------------------------------------------------------+
//| Process new 4H bar (main strategy logic)                        |
//+------------------------------------------------------------------+
void CFibReversalExpert::ProcessNewBar(void)
{
   // Get current bar index (0 = current bar, 1 = previous bar, etc.)
   int current_bar = 0;

   // Check if we have enough bars for 3-bar pattern (need at least bars 0, 1, 2)
   if(iBars(NULL, PERIOD_H4) < 3)
      return;

   // Process pattern detection (Python: for i in range(2, len(df)))
   CheckPatternDetection(current_bar);

   // Check active position exit conditions
   if(m_aktif_fib != NULL)
      CheckActivePositionExit(current_bar);

   // Check fibonacci activation
   if(m_aday_fib != NULL)
      CheckFibonacciActivation(current_bar);

   // Update peak/trough tracking
   UpdatePeakTroughTracking(current_bar);
}
//+------------------------------------------------------------------+
//| Check pattern detection (Python: 3-bar close patterns)         |
//+------------------------------------------------------------------+
void CFibReversalExpert::CheckPatternDetection(int bar_index)
{
   // Get 3-bar close pattern (Python: close0, close1, close2)
   double close0 = iClose(NULL, PERIOD_H4, bar_index + 2); // 2 bars ago
   double close1 = iClose(NULL, PERIOD_H4, bar_index + 1); // 1 bar ago
   double close2 = iClose(NULL, PERIOD_H4, bar_index);     // Current bar

   // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor'
   if(close0 > close1 && close1 > close2 && m_mode != MODE_YUKSELIS_FIBI_ARIYOR)
   {
      m_mode = MODE_YUKSELIS_FIBI_ARIYOR;

      // Python: kilitli_tepe_fiyat = df['High'].iloc[i-2:i+1].max()
      double high0 = iHigh(NULL, PERIOD_H4, bar_index + 2);
      double high1 = iHigh(NULL, PERIOD_H4, bar_index + 1);
      double high2 = iHigh(NULL, PERIOD_H4, bar_index);

      m_kilitli_tepe_fiyat = MathMax(high0, MathMax(high1, high2));
      m_kilitli_tepe_zaman = iTime(NULL, PERIOD_H4, bar_index + 2); // Use oldest bar time

      printf("Mode switched to Yükseliş Fibi Arıyor - Peak locked at %.5f", m_kilitli_tepe_fiyat);
   }
   // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor'
   else if(close0 < close1 && close1 < close2 && m_mode != MODE_DUSUS_FIBI_ARIYOR)
   {
      m_mode = MODE_DUSUS_FIBI_ARIYOR;

      // Python: kilitli_dip_fiyat = df['Low'].iloc[i-2:i+1].min()
      double low0 = iLow(NULL, PERIOD_H4, bar_index + 2);
      double low1 = iLow(NULL, PERIOD_H4, bar_index + 1);
      double low2 = iLow(NULL, PERIOD_H4, bar_index);

      m_kilitli_dip_fiyat = MathMin(low0, MathMin(low1, low2));
      m_kilitli_dip_zaman = iTime(NULL, PERIOD_H4, bar_index + 2); // Use oldest bar time

      printf("Mode switched to Düşüş Fibi Arıyor - Trough locked at %.5f", m_kilitli_dip_fiyat);
   }
}

//+------------------------------------------------------------------+
//| Update peak/trough tracking (Python style)                     |
//+------------------------------------------------------------------+
void CFibReversalExpert::UpdatePeakTroughTracking(int bar_index)
{
   double current_high = iHigh(NULL, PERIOD_H4, bar_index);
   double current_low = iLow(NULL, PERIOD_H4, bar_index);
   datetime current_time = iTime(NULL, PERIOD_H4, bar_index);

   if(m_mode == MODE_YUKSELIS_FIBI_ARIYOR)
   {
      // Python: if gecici_tepe_kilit: kilitli_tepe_fiyat, kilitli_tepe_zaman = gecici_tepe_kilit
      if(m_gecici_tepe_kilit > 0)
      {
         m_kilitli_tepe_fiyat = m_gecici_tepe_kilit;
         m_kilitli_tepe_zaman = current_time;
         m_gecici_tepe_kilit = 0;
         printf("Peak updated to %.5f (Yükseliş mode)", m_kilitli_tepe_fiyat);
      }

      // Python: if row['High'] > kilitli_tepe_fiyat: kilitli_tepe_fiyat, kilitli_tepe_zaman = row['High'], current_time
      if(current_high > m_kilitli_tepe_fiyat)
      {
         m_kilitli_tepe_fiyat = current_high;
         m_kilitli_tepe_zaman = current_time;
         printf("Peak updated to %.5f (Yükseliş mode)", m_kilitli_tepe_fiyat);
      }
      // Python: elif kilitli_tepe_zaman: (check if we can create fibonacci candidate)
      else if(m_kilitli_tepe_zaman > 0)
      {
         // Python: fark = (kilitli_tepe_fiyat - row['Low']) / row['Low']
         double fark = (m_kilitli_tepe_fiyat - current_low) / current_low;
         if(fark >= InpMinYuzdeFark / 100.0)
         {
            // Create candidate fibonacci
            if(m_aday_fib != NULL)
            {
               delete m_aday_fib;
               m_aday_fib = NULL;
            }
            m_aday_fib = new CFibonacciRetracement(m_kilitli_tepe_fiyat, m_kilitli_tepe_zaman, current_low, current_time, "Yükseliş");
            printf("Yükseliş candidate created: Peak=%.5f, Trough=%.5f, Change=%.2f%%, Fib236=%.5f, Fib500=%.5f",
                   m_kilitli_tepe_fiyat, current_low, fark * 100.0, m_aday_fib.level_236, m_aday_fib.level_500);
         }
      }
   }
   else if(m_mode == MODE_DUSUS_FIBI_ARIYOR)
   {
      // Python: if gecici_dip_kilit: kilitli_dip_fiyat, kilitli_dip_zaman = gecici_dip_kilit
      if(m_gecici_dip_kilit > 0)
      {
         m_kilitli_dip_fiyat = m_gecici_dip_kilit;
         m_kilitli_dip_zaman = current_time;
         m_gecici_dip_kilit = 0;
         printf("Trough updated to %.5f (Düşüş mode)", m_kilitli_dip_fiyat);
      }

      // Python: if row['Low'] < kilitli_dip_fiyat: kilitli_dip_fiyat, kilitli_dip_zaman = row['Low'], current_time
      if(current_low < m_kilitli_dip_fiyat)
      {
         m_kilitli_dip_fiyat = current_low;
         m_kilitli_dip_zaman = current_time;
         printf("Trough updated to %.5f (Düşüş mode)", m_kilitli_dip_fiyat);
      }
      // Python: elif kilitli_dip_zaman: (check if we can create fibonacci candidate)
      else if(m_kilitli_dip_zaman > 0)
      {
         // Python: fark = (row['High'] - kilitli_dip_fiyat) / kilitli_dip_fiyat
         double fark = (current_high - m_kilitli_dip_fiyat) / m_kilitli_dip_fiyat;
         if(fark >= InpMinYuzdeFark / 100.0)
         {
            // Create candidate fibonacci
            if(m_aday_fib != NULL)
            {
               delete m_aday_fib;
               m_aday_fib = NULL;
            }
            m_aday_fib = new CFibonacciRetracement(current_high, current_time, m_kilitli_dip_fiyat, m_kilitli_dip_zaman, "Düşüş");
            printf("Düşüş candidate created: Peak=%.5f, Trough=%.5f, Change=%.2f%%, Fib236=%.5f, Fib500=%.5f",
                   current_high, m_kilitli_dip_fiyat, fark * 100.0, m_aday_fib.level_236, m_aday_fib.level_500);
         }
      }
   }
}
//+------------------------------------------------------------------+
//| Check fibonacci activation (Python: aday_fib -> aktif_fib)     |
//+------------------------------------------------------------------+
void CFibReversalExpert::CheckFibonacciActivation(int bar_index)
{
   if(m_aday_fib == NULL)
      return;

   double current_close = iClose(NULL, PERIOD_H4, bar_index);

   if(m_aday_fib.type == "Yükseliş")
   {
      // Python: if row['Close'] < aday_fib.dip_fiyat or row['Close'] > aday_fib.tepe_fiyat: aday_fib = None
      if(current_close < m_aday_fib.dip_fiyat || current_close > m_aday_fib.tepe_fiyat)
      {
         printf("Yükseliş candidate invalidated - Close %.5f outside range [%.5f - %.5f]",
                current_close, m_aday_fib.dip_fiyat, m_aday_fib.tepe_fiyat);
         delete m_aday_fib;
         m_aday_fib = NULL;
      }
      // Python: elif row['Close'] > aday_fib.level_236: aktif_fib = aday_fib; aday_fib = None
      else if(current_close > m_aday_fib.level_236)
      {
         printf("Yükseliş fibonacci activated - Close %.5f > Fib236 %.5f", current_close, m_aday_fib.level_236);

         // Move candidate to active
         if(m_aktif_fib != NULL)
            delete m_aktif_fib;
         m_aktif_fib = m_aday_fib;
         m_aday_fib = NULL;

         // Open long position
         OpenLongPosition();
      }
   }
   else // Düşüş
   {
      // Python: if row['Close'] > aday_fib.tepe_fiyat or row['Close'] < aday_fib.dip_fiyat: aday_fib = None
      if(current_close > m_aday_fib.tepe_fiyat || current_close < m_aday_fib.dip_fiyat)
      {
         printf("Düşüş candidate invalidated - Close %.5f outside range [%.5f - %.5f]",
                current_close, m_aday_fib.dip_fiyat, m_aday_fib.tepe_fiyat);
         delete m_aday_fib;
         m_aday_fib = NULL;
      }
      // Python: elif row['Close'] < aday_fib.level_236: aktif_fib = aday_fib; aday_fib = None
      else if(current_close < m_aday_fib.level_236)
      {
         printf("Düşüş fibonacci activated - Close %.5f < Fib236 %.5f", current_close, m_aday_fib.level_236);

         // Move candidate to active
         if(m_aktif_fib != NULL)
            delete m_aktif_fib;
         m_aktif_fib = m_aday_fib;
         m_aday_fib = NULL;

         // Open short position
         OpenShortPosition();
      }
   }
}
//+------------------------------------------------------------------+
//| Check active position exit conditions (Python style)           |
//+------------------------------------------------------------------+
void CFibReversalExpert::CheckActivePositionExit(int bar_index)
{
   if(m_aktif_fib == NULL || m_currentTicket == 0)
      return;

   double current_high = iHigh(NULL, PERIOD_H4, bar_index);
   double current_low = iLow(NULL, PERIOD_H4, bar_index);

   bool success = false;
   bool fail = false;
   double kar_orani = 0;

   if(m_aktif_fib.type == "Yükseliş")
   {
      // Python: if row['Low'] < aktif_fib.level_236 * (1 - HARD_STOP_YUZDE): fail = True
      if(current_low < m_aktif_fib.level_236 * (1.0 - InpHardStopYuzde / 100.0))
      {
         fail = true;
         kar_orani = -InpHardStopYuzde;
      }
      // Python: elif row['High'] >= aktif_fib.level_500: success = True
      else if(current_high >= m_aktif_fib.level_500)
      {
         success = true;
         kar_orani = (m_aktif_fib.level_500 - m_aktif_fib.level_236) / m_aktif_fib.level_236 * 100.0;
      }
   }
   else // Düşüş
   {
      // Python: if row['High'] > aktif_fib.level_236 * (1 + HARD_STOP_YUZDE): fail = True
      if(current_high > m_aktif_fib.level_236 * (1.0 + InpHardStopYuzde / 100.0))
      {
         fail = true;
         kar_orani = -InpHardStopYuzde;
      }
      // Python: elif row['Low'] <= aktif_fib.level_500: success = True
      else if(current_low <= m_aktif_fib.level_500)
      {
         success = true;
         kar_orani = (m_aktif_fib.level_236 - m_aktif_fib.level_500) / m_aktif_fib.level_236 * 100.0;
      }
   }

   if(success || fail)
   {
      string reason = success ? "Fib Tamamlandı" : "Hard Stop";
      ClosePosition(reason, kar_orani);
   }
}

//+------------------------------------------------------------------+
//| Calculate lot size based on risk percentage                     |
//+------------------------------------------------------------------+
double CFibReversalExpert::CalculateLotSize(void)
{
   // Calculate lot size based on risk percentage
   double accountBalance = m_account.Balance();
   double riskAmount = accountBalance * (InpRiskPercent / 100.0);

   // Get symbol specifications
   double tickValue = m_symbol.TickValue();
   double tickSize = m_symbol.TickSize();

   double lotSize = 0.02; // Default lot size

   if(tickSize > 0 && tickValue > 0)
   {
      // Simple calculation - can be enhanced later
      double valuePerPoint = (tickValue / tickSize);
      double stopLossDistance = m_symbol.Ask() * InpHardStopYuzde / 100.0;

      if(stopLossDistance > 0)
         lotSize = riskAmount / (stopLossDistance * valuePerPoint);
   }

   // Normalize lot size to symbol specifications
   double minLot = m_symbol.LotsMin();
   double maxLot = m_symbol.LotsMax();
   double lotStep = m_symbol.LotsStep();

   // Round to lot step
   lotSize = MathRound(lotSize / lotStep) * lotStep;

   // Ensure lot size is within limits
   lotSize = MathMax(minLot, MathMin(maxLot, lotSize));

   printf("Lot size calculation: Balance=%.2f, Risk=%.2f USD, Lot=%.2f",
          accountBalance, riskAmount, lotSize);

   return lotSize;
}

//+------------------------------------------------------------------+
//| Open long position                                               |
//+------------------------------------------------------------------+
void CFibReversalExpert::OpenLongPosition(void)
{
   if(m_currentTicket != 0) // Already have a position
      return;

   double lotSize = CalculateLotSize();
   double ask = m_symbol.Ask();

   if(m_trade.Buy(lotSize, Symbol(), ask))
   {
      m_currentTicket = m_trade.ResultOrder();
      printf("Long position opened: Ticket=%I64u, Entry=%.5f, Lot=%.2f",
             m_currentTicket, ask, lotSize);
   }
   else
   {
      printf("Error opening long position: %s", m_trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
//| Open short position                                              |
//+------------------------------------------------------------------+
void CFibReversalExpert::OpenShortPosition(void)
{
   if(m_currentTicket != 0) // Already have a position
      return;

   double lotSize = CalculateLotSize();
   double bid = m_symbol.Bid();

   if(m_trade.Sell(lotSize, Symbol(), bid))
   {
      m_currentTicket = m_trade.ResultOrder();
      printf("Short position opened: Ticket=%I64u, Entry=%.5f, Lot=%.2f",
             m_currentTicket, bid, lotSize);
   }
   else
   {
      printf("Error opening short position: %s", m_trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
//| Python-style pattern detection using 3-bar close patterns      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::DetectFractals(void)
{


   // No need for additional bar time check - OnTick already handles this
   // Process pattern detection directly

   // Check if we have enough 4H bars for 3-bar pattern detection
   int barsCount = iBars(NULL, PERIOD_H4);
   if(barsCount < 5)
   {
      static bool warningShown = false;
      if(!warningShown)
      {
         printf("DEBUG: Not enough 4H bars for pattern detection. Current: %d, Need: 5", barsCount);
         warningShown = true;
      }
      return false;
   }

   // Get the last 3 completed 4H close prices (Python style: close0, close1, close2)
   double close0 = iClose(NULL, PERIOD_H4, 2); // 2 bars ago
   double close1 = iClose(NULL, PERIOD_H4, 1); // 1 bar ago
   double close2 = iClose(NULL, PERIOD_H4, 0); // Current bar (just completed)

   printf("DEBUG: 3-bar pattern check - close0=%.5f, close1=%.5f, close2=%.5f", close0, close1, close2);

   // Check for mode switching patterns (Python logic)
   bool bullishPattern = (close0 > close1 && close1 > close2); // Bearish trend → look for bullish fib
   bool bearishPattern = (close0 < close1 && close1 < close2); // Bullish trend → look for bearish fib

   // Python style: Only detect new patterns when NOT already in fibonacci tracking mode
   // Python: if close0 > close1 > close2 and mode != 'Yükseliş Fibi Arıyor'
   if(bullishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      printf("BULLISH PATTERN DETECTED: close0 > close1 > close2 - switching to look for bullish fib");
      m_tradingPhase = PHASE_ACTIVE_LONG;

      // Lock peak from last 3 bars (Python style) - this is the initial peak
      double high0 = iHigh(NULL, PERIOD_H4, 2);
      double high1 = iHigh(NULL, PERIOD_H4, 1);
      double high2 = iHigh(NULL, PERIOD_H4, 0);

      m_fibHighPrice = MathMax(high0, MathMax(high1, high2));

      // Initialize trough to current low (will be updated as we find lower lows)
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);

      m_fibLowPrice = MathMin(low0, MathMin(low1, low2));

      printf("Peak locked at %.5f, initial trough at %.5f from 3-bar pattern", m_fibHighPrice, m_fibLowPrice);
      return true;
   }
   // Python style: Only detect new patterns when NOT already in fibonacci tracking mode
   // Python: elif close0 < close1 < close2 and mode != 'Düşüş Fibi Arıyor'
   else if(bearishPattern && m_tradingPhase == PHASE_PASSIVE)
   {
      printf("BEARISH PATTERN DETECTED: close0 < close1 < close2 - switching to look for bearish fib");
      m_tradingPhase = PHASE_ACTIVE_SHORT;

      // Lock trough from last 3 bars (Python style) - this is the initial trough
      double low0 = iLow(NULL, PERIOD_H4, 2);
      double low1 = iLow(NULL, PERIOD_H4, 1);
      double low2 = iLow(NULL, PERIOD_H4, 0);

      m_fibLowPrice = MathMin(low0, MathMin(low1, low2));

      // This method is no longer used - replaced by new Python-style logic
   }
}

//+------------------------------------------------------------------+
//| Close position with reason and profit calculation               |
//+------------------------------------------------------------------+
void CFibReversalExpert::ClosePosition(string reason, double kar_orani)
{
   if(m_currentTicket == 0)
      return;

   if(m_trade.PositionClose(Symbol()))
   {
      // Update capital (Python style)
      m_sermaye *= 1.0 + (kar_orani / 100.0) * InpKaldirac;

      printf("Position closed: Reason=%s, Profit=%.2f%%, New Capital=%.2f",
             reason, kar_orani, m_sermaye);

      m_currentTicket = 0;

      // Reset active fibonacci (Python: aktif_fib = None)
      if(m_aktif_fib != NULL)
      {
         delete m_aktif_fib;
         m_aktif_fib = NULL;
      }
   }
   else
   {
      printf("Error closing position: %s", m_trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
//| Reset fibonacci objects                                          |
//+------------------------------------------------------------------+
void CFibReversalExpert::ResetFibonacci(void)
{
   if(m_aktif_fib != NULL)
   {
      delete m_aktif_fib;
      m_aktif_fib = NULL;
   }
   if(m_aday_fib != NULL)
   {
      delete m_aday_fib;
      m_aday_fib = NULL;
   }
}
//+------------------------------------------------------------------+
//| Main processing method (Python style)                           |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Processing(void)
{
   // Refresh market data
   if(!m_symbol.RefreshRates())
      return false;

   // Process new bar logic
   ProcessNewBar();

   return true;
}

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit(void)
{
   if(!ExtExpert.Init())
      return INIT_FAILED;
   return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert tick handler (Python style - process every new 4H bar)  |
//+------------------------------------------------------------------+
void OnTick(void)
{
   // Process on every new 4H bar (Python style)
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);

   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      ExtExpert.Processing();
   }
}
{
   // Only update in active phase
   if(m_tradingPhase == PHASE_PASSIVE)
      return false;

   // Get current bar data
   double currentHigh = iHigh(NULL, PERIOD_H4, 0); // Current bar (just completed)
   double currentLow = iLow(NULL, PERIOD_H4, 0);
   datetime currentTime = iTime(NULL, PERIOD_H4, 0);

   bool rangeUpdated = false;

   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // REMOVED: Aggressive fibonacci reset logic that was preventing trades
      // The Python script keeps fibonacci levels active longer
      // Original logic was too restrictive and caused 173 resets, missing 240+ trades
      double currentClose = iClose(NULL, PERIOD_H4, 0);

      // Looking for bullish fib - continuously update peak like Python script
      // Python: if row['High'] > kilitli_tepe_fiyat: kilitli_tepe_fiyat = row['High']
      if(currentHigh > m_fibHighPrice)
      {
         m_fibHighPrice = currentHigh;
         printf("Peak updated to %.5f (bullish mode)", m_fibHighPrice);
      }

      // Check if we meet the minimum range requirement using current peak and current low
      double priceChange = (m_fibHighPrice - currentLow) / currentLow * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(m_fibLowPrice != currentLow || m_fibLevel236 == 0)
         {
            m_fibLowPrice = currentLow;
            // Lock original values for reset logic when fibonacci candidate is first created
            if(m_fibLevel236 == 0)
            {
               m_fibOriginalHigh = m_fibHighPrice;
               m_fibOriginalLow = m_fibLowPrice;
               printf("Fibonacci candidate created - Original range locked: [%.5f - %.5f]",
                      m_fibOriginalLow, m_fibOriginalHigh);
            }
            printf("Bullish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange);
            CalculateFibonacciLevels();
            rangeUpdated = true;
         }
      }
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // REMOVED: Aggressive fibonacci reset logic that was preventing trades
      // The Python script keeps fibonacci levels active longer
      // Original logic was too restrictive and caused 173 resets, missing 240+ trades
      double currentClose = iClose(NULL, PERIOD_H4, 0);

      // Looking for bearish fib - continuously update trough like Python script
      // Python: if row['Low'] < kilitli_dip_fiyat: kilitli_dip_fiyat = row['Low']
      if(currentLow < m_fibLowPrice)
      {
         m_fibLowPrice = currentLow;
         printf("Trough updated to %.5f (bearish mode)", m_fibLowPrice);
      }

      // Check if we meet the minimum range requirement using current high and current trough
      double priceChange = (currentHigh - m_fibLowPrice) / m_fibLowPrice * 100.0;
      if(priceChange >= InpPriceChangePercentage)
      {
         if(m_fibHighPrice != currentHigh || m_fibLevel236 == 0)
         {
            m_fibHighPrice = currentHigh;
            // Lock original values for reset logic when fibonacci candidate is first created
            if(m_fibLevel236 == 0)
            {
               m_fibOriginalHigh = m_fibHighPrice;
               m_fibOriginalLow = m_fibLowPrice;
               printf("Fibonacci candidate created - Original range locked: [%.5f - %.5f]",
                      m_fibOriginalLow, m_fibOriginalHigh);
            }
            printf("Bearish fib candidate: Peak=%.5f, Trough=%.5f, Change=%.2f%%",
                   m_fibHighPrice, m_fibLowPrice, priceChange);
            CalculateFibonacciLevels();
            rangeUpdated = true;
         }
      }
   }

   // CRITICAL FIX: Add logic to reset to passive phase to allow detection of new patterns
   // This is essential for detecting both bullish and bearish patterns like the Python script

   // Check if current fibonacci setup has become invalid and should reset to passive
   double currentClose = iClose(NULL, PERIOD_H4, 0);
   bool shouldReset = false;

   if(m_tradingPhase == PHASE_ACTIVE_LONG)
   {
      // For long setups: Reset if price moves significantly below the fibonacci low
      // This indicates the bullish setup is invalidated
      if(currentClose < m_fibLowPrice * 0.98) // 2% below fibonacci low
      {
         shouldReset = true;
         printf("Long setup invalidated: Price %.5f moved below fib low %.5f - resetting to passive",
                currentClose, m_fibLowPrice);
      }
   }
   else if(m_tradingPhase == PHASE_ACTIVE_SHORT)
   {
      // For short setups: Reset if price moves significantly above the fibonacci high
      // This indicates the bearish setup is invalidated
      if(currentClose > m_fibHighPrice * 1.02) // 2% above fibonacci high
      {
         shouldReset = true;
         printf("Short setup invalidated: Price %.5f moved above fib high %.5f - resetting to passive",
                currentClose, m_fibHighPrice);
      }
   }

   if(shouldReset)
   {
      ResetStrategy();
      return true; // Strategy was reset
   }

   return rangeUpdated;
}




//+------------------------------------------------------------------+
//| Open long position (Python style - immediate activation)       |
//+------------------------------------------------------------------+
bool CFibReversalExpert::LongOpened(void)
{
   if(m_tradingPhase != PHASE_ACTIVE_LONG)
      return false;

   // Check if we have valid fibonacci levels
   if(m_fibLevel236 <= 0 || m_fibLevel500 <= 0)
      return false;

   // CRITICAL FIX: Check if position already exists - only one position per fibonacci setup
   if(m_longTicket != -1)
   {
      // Position already exists for this fibonacci setup, don't open another
      return false;
   }

   // Python style activation: Check if close price crosses above 236 level
   double close4h = iClose(NULL, PERIOD_H4, 0); // Current bar close (just completed)

   // CRITICAL FIX: Prevent immediate re-entry after position closure
   // Wait for price to move away from fibonacci level and come back (like Python script)
   if(m_positionJustClosed)
   {
      if(close4h <= m_fibLevel236)
      {
         // Price moved away from fibonacci level, reset the flag
         m_positionJustClosed = false;
         printf("Price moved away from Fib 236 level - ready for new entries");
      }
      else
      {
         // Price still above fibonacci level, wait for it to move away first
         return false;
      }
   }

   // Python style reset logic: Check if price moved outside fibonacci range
   // Python: if row['Close'] < aday_fib.dip_fiyat or row['Close'] > aday_fib.tepe_fiyat: aday_fib = None
   if(close4h < m_fibLowPrice || close4h > m_fibHighPrice)
   {
      printf("Fibonacci setup invalidated - Close %.5f outside range [%.5f - %.5f], resetting to passive",
             close4h, m_fibLowPrice, m_fibHighPrice);
      ResetStrategy();
      return false;
   }

   if(close4h > m_fibLevel236)
   {
      printf("BULLISH ACTIVATION: Close %.5f > Fib 236 %.5f - opening long position", close4h, m_fibLevel236);

      double ask = m_symbol.Ask();

      // Calculate lot size based on hard stop distance
      double hardStopDistance = ask * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_longEntryPrice = ask;

      printf("Opening long position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             ask, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Buy(lotSize, Symbol(), ask))
      {
         m_longTicket = m_trade.ResultOrder();
         printf("Long position opened successfully, ticket: %I64u", m_longTicket);
         return true;
      }
      else
      {
         printf("Error opening long position: %s", m_trade.ResultComment());
      }
   }

   return false;
}
//+------------------------------------------------------------------+
//| Open short position (Python style - immediate activation)      |
//+------------------------------------------------------------------+
bool CFibReversalExpert::ShortOpened(void)
{
   if(m_tradingPhase != PHASE_ACTIVE_SHORT)
      return false;

   // Check if we have valid fibonacci levels
   if(m_fibLevel236 <= 0 || m_fibLevel500 <= 0)
      return false;

   // CRITICAL FIX: Check if position already exists - only one position per fibonacci setup
   if(m_shortTicket != -1)
   {
      // Position already exists for this fibonacci setup, don't open another
      return false;
   }

   // Python style activation: Check if close price crosses below 236 level
   double close4h = iClose(NULL, PERIOD_H4, 0); // Current bar close (just completed)

   // CRITICAL FIX: Prevent immediate re-entry after position closure
   // Wait for price to move away from fibonacci level and come back (like Python script)
   if(m_positionJustClosed)
   {
      if(close4h >= m_fibLevel236)
      {
         // Price moved away from fibonacci level, reset the flag
         m_positionJustClosed = false;
         printf("Price moved away from Fib 236 level - ready for new entries");
      }
      else
      {
         // Price still below fibonacci level, wait for it to move away first
         return false;
      }
   }

   // Python style reset logic: Check if price moved outside fibonacci range
   // Python: if row['Close'] > aday_fib.tepe_fiyat or row['Close'] < aday_fib.dip_fiyat: aday_fib = None
   if(close4h > m_fibHighPrice || close4h < m_fibLowPrice)
   {
      printf("Fibonacci setup invalidated - Close %.5f outside range [%.5f - %.5f], resetting to passive",
             close4h, m_fibLowPrice, m_fibHighPrice);
      ResetStrategy();
      return false;
   }

   if(close4h < m_fibLevel236)
   {
      printf("BEARISH ACTIVATION: Close %.5f < Fib 236 %.5f - opening short position", close4h, m_fibLevel236);

      double bid = m_symbol.Bid();

      // Calculate lot size based on hard stop distance
      double hardStopDistance = bid * InpHardStopPercentage / 100.0;
      double lotSize = CalculateLotSize(hardStopDistance);

      m_shortEntryPrice = bid;

      printf("Opening short position: Entry=%.5f, Fib236=%.5f, Fib500=%.5f, Lot=%.2f",
             bid, m_fibLevel236, m_fibLevel500, lotSize);

      if(m_trade.Sell(lotSize, Symbol(), bid))
      {
         m_shortTicket = m_trade.ResultOrder();
         printf("Short position opened successfully, ticket: %I64u", m_shortTicket);
         return true;
      }
      else
      {
         printf("Error opening short position: %s", m_trade.ResultComment());
      }
   }

   return false;
}
//+------------------------------------------------------------------+
//| Log heartbeat with EA status and position information           |
//+------------------------------------------------------------------+
void CFibReversalExpert::LogHeartbeat(void)
{
   // Update heartbeat timestamp
   m_lastHeartbeatTime = TimeCurrent();

   // Get account information
   double balance = m_account.Balance();
   double equity = m_account.Equity();
   double freeMargin = m_account.FreeMargin();

   // Count positions for this symbol
   int posCount = 0;
   string posInfo = "";

   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         m_position.SelectByIndex(i);
         posCount++;

         string posType = (m_position.PositionType() == POSITION_TYPE_BUY) ? "LONG" : "SHORT";
         double openPrice = m_position.PriceOpen();
         double lotSize = m_position.Volume();
         double profit = m_position.Profit();
         datetime openTime = m_position.Time();

         // Calculate SL/TP based on strategy
         double sl = 0;
         double tp = 0;

         if(m_position.PositionType() == POSITION_TYPE_BUY)
         {
            sl = m_longEntryPrice * (1.0 - InpHardStopPercentage / 100.0);
            tp = m_fibLevel500;
         }
         else
         {
            sl = m_shortEntryPrice * (1.0 + InpHardStopPercentage / 100.0);
            tp = m_fibLevel500;
         }

         // Calculate position duration
         int durationHours = (int)((TimeCurrent() - openTime) / 3600);
         int durationMins = (int)(((TimeCurrent() - openTime) % 3600) / 60);

         posInfo += StringFormat("%s: %.2f lots @ %.5f, P&L: %.2f, SL: %.5f, TP: %.5f, Duration: %dh%dm | ",
                                posType, lotSize, openPrice, profit, sl, tp, durationHours, durationMins);
      }
   }

   // Log heartbeat information
   printf("=== HEARTBEAT === %s ===", TimeToString(TimeCurrent(), TIME_DATE|TIME_MINUTES));
   printf("Account: Balance=%.2f, Equity=%.2f, Free Margin=%.2f", balance, equity, freeMargin);
   printf("Positions: %d active | %s", posCount, (posCount > 0) ? posInfo : "No positions");

   // Log strategy status
   string phaseStr = (m_tradingPhase == PHASE_PASSIVE) ? "PASSIVE" :
                    (m_tradingPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT";

   if(m_tradingPhase == PHASE_PASSIVE)
   {
      // In passive phase
      printf("Strategy: Phase=%s, Looking for 3-bar patterns", phaseStr);
   }
   else
   {
      // In active phase, show the current Fibonacci range
      printf("Strategy: Phase=%s, High=%.5f, Low=%.5f, Fib236=%.5f, Fib500=%.5f",
             phaseStr, m_fibHighPrice, m_fibLowPrice, m_fibLevel236, m_fibLevel500);
   }

   printf("Market: Bid=%.5f, Ask=%.5f, Spread=%.1f points",
          m_symbol.Bid(), m_symbol.Ask(), m_symbol.Spread());
   printf("EA Status: Risk=%.1f%%, Price Change=%.1f%%, Hard Stop=%.1f%%",
          InpRiskPercent, InpPriceChangePercentage, InpHardStopPercentage);
   printf("=== END HEARTBEAT ===");
}






//+------------------------------------------------------------------+
//| main function returns true if any position processed             |
//+------------------------------------------------------------------+
bool CFibReversalExpert::Processing(void)
  {
   // Refresh market data
   if(!m_symbol.RefreshRates())
      return(false);

   // Debug: Track processing calls
   static int processingCounter = 0;
   processingCounter++;
   if(processingCounter % 10000 == 0) // Every 10000 calls
   {
      printf("DEBUG: Processing called %d times, Current phase: %s",
             processingCounter,
             (m_tradingPhase == PHASE_PASSIVE) ? "PASSIVE" :
             (m_tradingPhase == PHASE_ACTIVE_LONG) ? "ACTIVE_LONG" : "ACTIVE_SHORT");
   }

   // Check for heartbeat logging (once per hour)
   datetime currentTime = TimeCurrent();
   if(m_lastHeartbeatTime == 0 || (currentTime - m_lastHeartbeatTime) >= m_heartbeatInterval)
   {
      LogHeartbeat();
   }

   // Count and identify positions for this symbol
   uint posNumber = 0;
   int posTotal = PositionsTotal();
   for(int i = 0; i < posTotal; i++)
   {
      if(PositionGetSymbol(i) == Symbol())
      {
         posNumber++;
         m_position.SelectByIndex(i);
         if(m_position.PositionType() == POSITION_TYPE_BUY)
            m_longTicket = m_position.Ticket();
         else if(m_position.PositionType() == POSITION_TYPE_SELL)
            m_shortTicket = m_position.Ticket();
      }
   }

   // Process positions based on count
   if(posNumber > 1)
   {
      Alert("Error: Multiple positions detected");
      return(false);
   }
   else if(posNumber == 1)
   {
      // Handle existing positions - check for closing conditions
      if(m_position.SelectByTicket(m_longTicket) && m_position.PositionType() == POSITION_TYPE_BUY)
      {
         if(LongClosed())
            return(true);
      }

      if(m_position.SelectByTicket(m_shortTicket) && m_position.PositionType() == POSITION_TYPE_SELL)
      {
         if(ShortClosed())
            return(true);
      }
   }
   else if(posNumber == 0)
   {
      // No positions - process strategy phases
      if(m_tradingPhase == PHASE_PASSIVE)
      {
         if(ProcessPassivePhase())
            return(true);
      }
      else
      {
         if(ProcessActivePhase())
            return(true);
      }
   }

   return(false);
  }
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit(void)
  {
//--- create all necessary objects
   if(!ExtExpert.Init())
      return(INIT_FAILED);
//--- succeed
   return(INIT_SUCCEEDED);
  }






//+------------------------------------------------------------------+
//| Expert tick handler (Python style - process every new 4H bar)  |
//+------------------------------------------------------------------+
void OnTick(void)
{
   // Process on every new 4H bar (Python style)
   static datetime lastBarTime = 0;
   datetime currentBarTime = iTime(NULL, PERIOD_H4, 0);

   if(currentBarTime != lastBarTime)
   {
      lastBarTime = currentBarTime;
      ExtExpert.Processing();
   }
}
